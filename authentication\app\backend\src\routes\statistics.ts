/**
 * 统计数据路由
 */

import { FastifyInstance, FastifyRequest, FastifyReply } from 'fastify';
import { prisma } from '@/config/database';
import { logger } from '@/utils/logger';
import { requireAdmin } from '@/middleware/auth';
import { adminActionRateLimit } from '@/middleware/rateLimit';

export async function statisticsRoutes(fastify: FastifyInstance) {
  // 获取仪表盘统计数据
  fastify.get('/dashboard', {
    preHandler: [fastify.authenticate, requireAdmin()],
    handler: async (request: FastifyRequest, reply: FastifyReply) => {
      try {
        const now = new Date();
        const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
        const thisWeek = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000);
        const thisMonth = new Date(now.getFullYear(), now.getMonth(), 1);
        const lastMonth = new Date(now.getFullYear(), now.getMonth() - 1, 1);

        // 获取基础统计数据
        const [
          totalUsers,
          activeUsers,
          pendingUsers,
          totalAdmins,
          totalInvites,
          activeInvites,
          todayRegistrations,
          weekRegistrations,
          monthRegistrations,
          lastMonthRegistrations,
        ] = await Promise.all([
          // 总用户数
          prisma.user.count({
            where: { deleted: false },
          }),
          
          // 活跃用户数
          prisma.user.count({
            where: {
              deleted: false,
              status: 'active',
            },
          }),
          
          // 待审核用户数
          prisma.pendingUser.count({
            where: {
              deleted: false,
              status: 'pending',
            },
          }),
          
          // 管理员总数
          prisma.admin.count({
            where: { isActive: true },
          }),
          
          // 邀请码总数
          prisma.invite.count(),
          
          // 活跃邀请码数
          prisma.invite.count({
            where: {
              isActive: true,
              expireAt: { gt: now },
            },
          }),
          
          // 今日注册数
          prisma.user.count({
            where: {
              deleted: false,
              createdAt: { gte: today },
            },
          }),
          
          // 本周注册数
          prisma.user.count({
            where: {
              deleted: false,
              createdAt: { gte: thisWeek },
            },
          }),
          
          // 本月注册数
          prisma.user.count({
            where: {
              deleted: false,
              createdAt: { gte: thisMonth },
            },
          }),
          
          // 上月注册数
          prisma.user.count({
            where: {
              deleted: false,
              createdAt: {
                gte: lastMonth,
                lt: thisMonth,
              },
            },
          }),
        ]);

        // 计算增长率
        const registrationGrowth = lastMonthRegistrations > 0 
          ? ((monthRegistrations - lastMonthRegistrations) / lastMonthRegistrations * 100)
          : 0;

        // 获取用户分类统计
        const categoryStats = await prisma.user.groupBy({
          by: ['category'],
          _count: {
            category: true,
          },
          where: {
            deleted: false,
          },
        });

        // 获取学校统计（前10）
        const schoolStats = await prisma.user.groupBy({
          by: ['school'],
          _count: {
            school: true,
          },
          where: {
            deleted: false,
            school: { not: '' },
          },
          orderBy: {
            _count: {
              school: 'desc',
            },
          },
          take: 10,
        });

        // 获取最近7天的注册趋势
        const registrationTrend = [];
        for (let i = 6; i >= 0; i--) {
          const date = new Date(today.getTime() - i * 24 * 60 * 60 * 1000);
          const nextDate = new Date(date.getTime() + 24 * 60 * 60 * 1000);
          
          const count = await prisma.user.count({
            where: {
              deleted: false,
              createdAt: {
                gte: date,
                lt: nextDate,
              },
            },
          });
          
          registrationTrend.push({
            date: date.toISOString().split('T')[0],
            count,
          });
        }

        // 获取最近活动
        const recentActivities = await prisma.auditLog.findMany({
          take: 10,
          orderBy: { createdAt: 'desc' },
          select: {
            id: true,
            action: true,
            createdAt: true,
            admin: {
              select: {
                username: true,
                email: true,
              },
            },
          },
        });

        return reply.code(200).send({
          success: true,
          data: {
            summary: {
              totalUsers,
              activeUsers,
              pendingUsers,
              totalAdmins,
              totalInvites,
              activeInvites,
              todayRegistrations,
              weekRegistrations,
              monthRegistrations,
              registrationGrowth: Math.round(registrationGrowth * 100) / 100,
            },
            categoryStats: categoryStats.map(stat => ({
              category: stat.category,
              count: stat._count.category,
            })),
            schoolStats: schoolStats.map(stat => ({
              school: stat.school,
              count: stat._count.school,
            })),
            registrationTrend,
            recentActivities,
          },
        });
      } catch (error) {
        logger.error('Get dashboard statistics error:', error);
        return reply.code(500).send({
          success: false,
          message: '获取仪表盘统计失败',
          error: 'INTERNAL_ERROR',
        });
      }
    },
  });

  // 获取用户统计详情
  fastify.get('/users', {
    preHandler: [fastify.authenticate, requireAdmin()],
    handler: async (request: FastifyRequest, reply: FastifyReply) => {
      try {
        const now = new Date();
        const thisMonth = new Date(now.getFullYear(), now.getMonth(), 1);

        // 获取用户状态统计
        const statusStats = await prisma.user.groupBy({
          by: ['status'],
          _count: {
            status: true,
          },
          where: {
            deleted: false,
          },
        });

        // 获取用户分类统计
        const categoryStats = await prisma.user.groupBy({
          by: ['category'],
          _count: {
            category: true,
          },
          where: {
            deleted: false,
          },
        });

        // 获取学校统计
        const schoolStats = await prisma.user.groupBy({
          by: ['school'],
          _count: {
            school: true,
          },
          where: {
            deleted: false,
            school: { not: '' },
          },
          orderBy: {
            _count: {
              school: 'desc',
            },
          },
        });

        // 获取年级统计
        const gradeStats = await prisma.user.groupBy({
          by: ['grade'],
          _count: {
            grade: true,
          },
          where: {
            deleted: false,
            grade: { not: null },
          },
          orderBy: {
            grade: 'asc',
          },
        });

        // 获取最近30天的注册趋势
        const registrationTrend = [];
        for (let i = 29; i >= 0; i--) {
          const date = new Date(now.getTime() - i * 24 * 60 * 60 * 1000);
          const nextDate = new Date(date.getTime() + 24 * 60 * 60 * 1000);
          
          const count = await prisma.user.count({
            where: {
              deleted: false,
              createdAt: {
                gte: date,
                lt: nextDate,
              },
            },
          });
          
          registrationTrend.push({
            date: date.toISOString().split('T')[0],
            count,
          });
        }

        return reply.code(200).send({
          success: true,
          data: {
            statusStats: statusStats.map(stat => ({
              status: stat.status,
              count: stat._count.status,
            })),
            categoryStats: categoryStats.map(stat => ({
              category: stat.category,
              count: stat._count.category,
            })),
            schoolStats: schoolStats.map(stat => ({
              school: stat.school,
              count: stat._count.school,
            })),
            gradeStats: gradeStats.map(stat => ({
              grade: stat.grade,
              count: stat._count.grade,
            })),
            registrationTrend,
          },
        });
      } catch (error) {
        logger.error('Get user statistics error:', error);
        return reply.code(500).send({
          success: false,
          message: '获取用户统计失败',
          error: 'INTERNAL_ERROR',
        });
      }
    },
  });

  // 获取审核统计
  fastify.get('/audit', {
    preHandler: [fastify.authenticate, requireAdmin()],
    handler: async (request: FastifyRequest, reply: FastifyReply) => {
      try {
        const now = new Date();
        const thisMonth = new Date(now.getFullYear(), now.getMonth(), 1);

        // 获取待审核用户统计
        const pendingStats = await prisma.pendingUser.groupBy({
          by: ['status'],
          _count: {
            status: true,
          },
          where: {
            deleted: false,
          },
        });

        // 获取分类统计
        const categoryStats = await prisma.pendingUser.groupBy({
          by: ['category'],
          _count: {
            category: true,
          },
          where: {
            deleted: false,
          },
        });

        // 获取审核员统计
        const auditorStats = await prisma.pendingUser.groupBy({
          by: ['auditedById'],
          _count: {
            auditedById: true,
          },
          where: {
            deleted: false,
            auditedById: { not: null },
            auditedAt: { gte: thisMonth },
          },
          orderBy: {
            _count: {
              auditedById: 'desc',
            },
          },
        });

        // 获取审核员信息
        const auditorIds = auditorStats.map(stat => stat.auditedById).filter(Boolean);
        const auditors = await prisma.admin.findMany({
          where: {
            id: { in: auditorIds as string[] },
          },
          select: {
            id: true,
            username: true,
            email: true,
          },
        });

        const auditorStatsWithNames = auditorStats.map(stat => ({
          ...stat,
          auditor: auditors.find(auditor => auditor.id === stat.auditedById),
        }));

        // 获取最近7天的审核趋势
        const auditTrend = [];
        for (let i = 6; i >= 0; i--) {
          const date = new Date(now.getTime() - i * 24 * 60 * 60 * 1000);
          const nextDate = new Date(date.getTime() + 24 * 60 * 60 * 1000);
          
          const [approved, rejected] = await Promise.all([
            prisma.pendingUser.count({
              where: {
                deleted: false,
                status: 'approved',
                auditedAt: {
                  gte: date,
                  lt: nextDate,
                },
              },
            }),
            prisma.pendingUser.count({
              where: {
                deleted: false,
                status: 'rejected',
                auditedAt: {
                  gte: date,
                  lt: nextDate,
                },
              },
            }),
          ]);
          
          auditTrend.push({
            date: date.toISOString().split('T')[0],
            approved,
            rejected,
            total: approved + rejected,
          });
        }

        return reply.code(200).send({
          success: true,
          data: {
            pendingStats: pendingStats.map(stat => ({
              status: stat.status,
              count: stat._count.status,
            })),
            categoryStats: categoryStats.map(stat => ({
              category: stat.category,
              count: stat._count.category,
            })),
            auditorStats: auditorStatsWithNames.map(stat => ({
              auditorId: stat.auditedById,
              auditor: stat.auditor,
              count: stat._count.auditedById,
            })),
            auditTrend,
          },
        });
      } catch (error) {
        logger.error('Get audit statistics error:', error);
        return reply.code(500).send({
          success: false,
          message: '获取审核统计失败',
          error: 'INTERNAL_ERROR',
        });
      }
    },
  });

  // 获取系统统计
  fastify.get('/system', {
    preHandler: [fastify.authenticate, requireAdmin()],
    handler: async (request: FastifyRequest, reply: FastifyReply) => {
      try {
        const now = new Date();
        const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());

        // 获取系统基础信息
        const [
          totalAuditLogs,
          todayAuditLogs,
          totalConfigs,
          activeAdmins,
        ] = await Promise.all([
          prisma.auditLog.count(),
          prisma.auditLog.count({
            where: {
              createdAt: { gte: today },
            },
          }),
          prisma.config.count(),
          prisma.admin.count({
            where: {
              isActive: true,
              lastLoginAt: {
                gte: new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000), // 30天内登录
              },
            },
          }),
        ]);

        // 获取最常用的操作
        const topActions = await prisma.auditLog.groupBy({
          by: ['action'],
          _count: {
            action: true,
          },
          orderBy: {
            _count: {
              action: 'desc',
            },
          },
          take: 10,
        });

        // 获取管理员活跃度
        const adminActivity = await prisma.auditLog.groupBy({
          by: ['adminId'],
          _count: {
            adminId: true,
          },
          where: {
            adminId: { not: null },
            createdAt: {
              gte: new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000), // 最近7天
            },
          },
          orderBy: {
            _count: {
              adminId: 'desc',
            },
          },
          take: 10,
        });

        // 获取管理员信息
        const adminIds = adminActivity.map(activity => activity.adminId).filter(Boolean);
        const admins = await prisma.admin.findMany({
          where: {
            id: { in: adminIds as string[] },
          },
          select: {
            id: true,
            username: true,
            email: true,
          },
        });

        const adminActivityWithNames = adminActivity.map(activity => ({
          ...activity,
          admin: admins.find(admin => admin.id === activity.adminId),
        }));

        return reply.code(200).send({
          success: true,
          data: {
            summary: {
              totalAuditLogs,
              todayAuditLogs,
              totalConfigs,
              activeAdmins,
            },
            topActions: topActions.map(action => ({
              action: action.action,
              count: action._count.action,
            })),
            adminActivity: adminActivityWithNames.map(activity => ({
              adminId: activity.adminId,
              admin: activity.admin,
              count: activity._count.adminId,
            })),
          },
        });
      } catch (error) {
        logger.error('Get system statistics error:', error);
        return reply.code(500).send({
          success: false,
          message: '获取系统统计失败',
          error: 'INTERNAL_ERROR',
        });
      }
    },
  });
}
