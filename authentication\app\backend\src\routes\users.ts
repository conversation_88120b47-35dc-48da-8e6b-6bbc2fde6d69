/**
 * 用户管理路由
 */

import { FastifyInstance, FastifyRequest, FastifyReply } from 'fastify';
import { prisma } from '@/config/database';
import { logger, SecurityLogger } from '@/utils/logger';
import { requireAdmin } from '@/middleware/auth';
import { adminActionRateLimit } from '@/middleware/rateLimit';
import {
  UserCreateSchema,
  UserUpdateSchema,
  PaginationSchema,
  IdParamSchema,
  BatchOperationSchema,
} from '@/schemas/auth';
import {
  UserCreateInput,
  UserUpdateInput,
  PaginationQuery,
  BatchOperationInput,
} from '@/types';

export async function userRoutes(fastify: FastifyInstance) {
  // 获取用户列表
  fastify.get<{ Querystring: PaginationQuery }>('/', {
    schema: PaginationSchema,
    preHandler: [fastify.authenticate, requireAdmin(), adminActionRateLimit],
    handler: async (request: FastifyRequest<{ Querystring: PaginationQuery }>, reply: FastifyReply) => {
      try {
        const {
          page = 1,
          limit = 20,
          search,
          category,
          status,
          sortBy = 'createdAt',
          sortOrder = 'desc',
        } = request.query;

        const skip = (page - 1) * limit;
        const where: any = {
          deleted: false,
        };

        // 搜索条件
        if (search) {
          where.OR = [
            { qq: { contains: search } },
            { realName: { contains: search } },
            { school: { contains: search } },
            { email: { contains: search } },
          ];
        }

        if (category) {
          where.category = category;
        }

        if (status) {
          where.status = status;
        }

        // 获取用户列表和总数
        const [users, total] = await Promise.all([
          prisma.user.findMany({
            where,
            skip,
            take: limit,
            orderBy: { [sortBy]: sortOrder },
            select: {
              id: true,
              qq: true,
              realName: true,
              school: true,
              category: true,
              status: true,
              email: true,
              phone: true,
              department: true,
              grade: true,
              createdAt: true,
              updatedAt: true,
            },
          }),
          prisma.user.count({ where }),
        ]);

        return reply.code(200).send({
          success: true,
          data: {
            users,
            pagination: {
              page,
              limit,
              total,
              pages: Math.ceil(total / limit),
            },
          },
        });
      } catch (error) {
        logger.error('Get users error:', error);
        return reply.code(500).send({
          success: false,
          message: '获取用户列表失败',
          error: 'INTERNAL_ERROR',
        });
      }
    },
  });

  // 获取单个用户详情
  fastify.get<{ Params: { id: string } }>('/:id', {
    schema: IdParamSchema,
    preHandler: [fastify.authenticate, requireAdmin()],
    handler: async (request: FastifyRequest<{ Params: { id: string } }>, reply: FastifyReply) => {
      try {
        const { id } = request.params;

        const user = await prisma.user.findFirst({
          where: {
            id,
            deleted: false,
          },
          include: {
            auditLogs: {
              take: 10,
              orderBy: { createdAt: 'desc' },
              select: {
                id: true,
                action: true,
                details: true,
                ipAddress: true,
                userAgent: true,
                createdAt: true,
                admin: {
                  select: {
                    username: true,
                    realName: true,
                  },
                },
              },
            },
          },
        });

        if (!user) {
          return reply.code(404).send({
            success: false,
            message: '用户不存在',
            error: 'USER_NOT_FOUND',
          });
        }

        return reply.code(200).send({
          success: true,
          data: user,
        });
      } catch (error) {
        logger.error('Get user error:', error);
        return reply.code(500).send({
          success: false,
          message: '获取用户详情失败',
          error: 'INTERNAL_ERROR',
        });
      }
    },
  });

  // 创建用户
  fastify.post<{ Body: UserCreateInput }>('/', {
    schema: UserCreateSchema,
    preHandler: [fastify.authenticate, requireAdmin('admin'), adminActionRateLimit],
    handler: async (request: FastifyRequest<{ Body: UserCreateInput }>, reply: FastifyReply) => {
      try {
        const userData = request.body;
        const admin = request.user as any;

        // 检查QQ号是否已存在
        const existingUser = await prisma.user.findFirst({
          where: {
            qq: userData.qq,
            deleted: false,
          },
        });

        if (existingUser) {
          return reply.code(409).send({
            success: false,
            message: 'QQ号已存在',
            error: 'QQ_EXISTS',
          });
        }

        // 检查邮箱是否已存在（如果提供）
        if (userData.email) {
          const existingEmail = await prisma.user.findFirst({
            where: {
              email: userData.email,
              deleted: false,
            },
          });

          if (existingEmail) {
            return reply.code(409).send({
              success: false,
              message: '邮箱已存在',
              error: 'EMAIL_EXISTS',
            });
          }
        }

        // 创建用户
        const user = await prisma.user.create({
          data: {
            ...userData,
            status: userData.status || 'active',
          },
        });

        // 记录审计日志
        await prisma.auditLog.create({
          data: {
            action: 'user_created',
            details: {
              userId: user.id,
              qq: user.qq,
              realName: user.realName,
              category: user.category,
            },
            adminId: admin.adminId,
            ipAddress: request.ip,
            userAgent: request.headers['user-agent'] || '',
          },
        });

        SecurityLogger.logSecurityEvent(
          'user_created',
          {
            userId: user.id,
            qq: user.qq,
            adminId: admin.adminId,
            ip: request.ip,
          },
          'low'
        );

        return reply.code(201).send({
          success: true,
          message: '用户创建成功',
          data: {
            id: user.id,
            qq: user.qq,
            realName: user.realName,
            status: user.status,
          },
        });
      } catch (error) {
        logger.error('Create user error:', error);
        return reply.code(500).send({
          success: false,
          message: '创建用户失败',
          error: 'INTERNAL_ERROR',
        });
      }
    },
  });

  // 更新用户
  fastify.put<{ Params: { id: string }; Body: UserUpdateInput }>('/:id', {
    schema: UserUpdateSchema,
    preHandler: [fastify.authenticate, requireAdmin('admin'), adminActionRateLimit],
    handler: async (request: FastifyRequest<{ Params: { id: string }; Body: UserUpdateInput }>, reply: FastifyReply) => {
      try {
        const { id } = request.params;
        const updateData = request.body;
        const admin = request.user as any;

        // 检查用户是否存在
        const existingUser = await prisma.user.findFirst({
          where: {
            id,
            deleted: false,
          },
        });

        if (!existingUser) {
          return reply.code(404).send({
            success: false,
            message: '用户不存在',
            error: 'USER_NOT_FOUND',
          });
        }

        // 检查邮箱是否已被其他用户使用
        if (updateData.email && updateData.email !== existingUser.email) {
          const emailExists = await prisma.user.findFirst({
            where: {
              email: updateData.email,
              deleted: false,
              id: { not: id },
            },
          });

          if (emailExists) {
            return reply.code(409).send({
              success: false,
              message: '邮箱已被其他用户使用',
              error: 'EMAIL_EXISTS',
            });
          }
        }

        // 更新用户
        const user = await prisma.user.update({
          where: { id },
          data: updateData,
        });

        // 记录审计日志
        await prisma.auditLog.create({
          data: {
            action: 'user_updated',
            details: {
              userId: user.id,
              changes: updateData,
              oldData: {
                realName: existingUser.realName,
                email: existingUser.email,
                status: existingUser.status,
              },
            },
            adminId: admin.adminId,
            ipAddress: request.ip,
            userAgent: request.headers['user-agent'] || '',
          },
        });

        SecurityLogger.logSecurityEvent(
          'user_updated',
          {
            userId: user.id,
            adminId: admin.adminId,
            changes: Object.keys(updateData),
            ip: request.ip,
          },
          'low'
        );

        return reply.code(200).send({
          success: true,
          message: '用户更新成功',
          data: user,
        });
      } catch (error) {
        logger.error('Update user error:', error);
        return reply.code(500).send({
          success: false,
          message: '更新用户失败',
          error: 'INTERNAL_ERROR',
        });
      }
    },
  });

  // 删除用户（软删除）
  fastify.delete<{ Params: { id: string } }>('/:id', {
    schema: IdParamSchema,
    preHandler: [fastify.authenticate],
    handler: async (request: FastifyRequest<{ Params: { id: string } }>, reply: FastifyReply) => {
      try {
        const { id } = request.params;
        const admin = request.user as any;

        // 检查用户是否存在
        const user = await prisma.user.findFirst({
          where: {
            id,
            deleted: false,
          },
        });

        if (!user) {
          return reply.code(404).send({
            success: false,
            message: '用户不存在',
            error: 'USER_NOT_FOUND',
          });
        }

        // 软删除用户
        await prisma.user.update({
          where: { id },
          data: { deleted: true },
        });

        // 记录审计日志
        await prisma.auditLog.create({
          data: {
            action: 'user_deleted',
            details: {
              userId: user.id,
              qq: user.qq,
              realName: user.realName,
            },
            adminId: admin.adminId,
            ipAddress: request.ip,
            userAgent: request.headers['user-agent'] || '',
          },
        });

        SecurityLogger.logSecurityEvent(
          'user_deleted',
          {
            userId: user.id,
            qq: user.qq,
            adminId: admin.adminId,
            ip: request.ip,
          },
          'medium'
        );

        return reply.code(200).send({
          success: true,
          message: '用户删除成功',
        });
      } catch (error) {
        logger.error('Delete user error:', error);
        return reply.code(500).send({
          success: false,
          message: '删除用户失败',
          error: 'INTERNAL_ERROR',
        });
      }
    },
  });

  // 批量操作
  fastify.post<{ Body: BatchOperationInput }>('/batch', {
    schema: BatchOperationSchema,
    preHandler: [fastify.authenticate, requireAdmin('admin'), adminActionRateLimit],
    handler: async (request: FastifyRequest<{ Body: BatchOperationInput }>, reply: FastifyReply) => {
      try {
        const { ids, action, data } = request.body;
        const admin = request.user as any;

        let result;
        let message;

        switch (action) {
          case 'delete':
            result = await prisma.user.updateMany({
              where: {
                id: { in: ids },
                deleted: false,
              },
              data: { deleted: true },
            });
            message = `成功删除 ${result.count} 个用户`;
            break;

          case 'activate':
            result = await prisma.user.updateMany({
              where: {
                id: { in: ids },
                deleted: false,
              },
              data: { status: 'active' },
            });
            message = `成功激活 ${result.count} 个用户`;
            break;

          case 'deactivate':
            result = await prisma.user.updateMany({
              where: {
                id: { in: ids },
                deleted: false,
              },
              data: { status: 'inactive' },
            });
            message = `成功停用 ${result.count} 个用户`;
            break;

          default:
            return reply.code(400).send({
              success: false,
              message: '不支持的批量操作',
              error: 'UNSUPPORTED_ACTION',
            });
        }

        // 记录审计日志
        await prisma.auditLog.create({
          data: {
            action: `batch_${action}`,
            details: {
              userIds: ids,
              count: result.count,
              data,
            },
            adminId: admin.adminId,
            ipAddress: request.ip,
            userAgent: request.headers['user-agent'] || '',
          },
        });

        SecurityLogger.logSecurityEvent(
          `batch_${action}`,
          {
            userIds: ids,
            count: result.count,
            adminId: admin.adminId,
            ip: request.ip,
          },
          'medium'
        );

        return reply.code(200).send({
          success: true,
          message,
          data: { count: result.count },
        });
      } catch (error) {
        logger.error('Batch operation error:', error);
        return reply.code(500).send({
          success: false,
          message: '批量操作失败',
          error: 'INTERNAL_ERROR',
        });
      }
    },
  });
}
