/**
 * 邀请码路由
 */

import { FastifyInstance, FastifyRequest, FastifyReply } from 'fastify';
import { prisma } from '@/config/database';
import { logger, SecurityLogger } from '@/utils/logger';
import { requireAdmin } from '@/middleware/auth';
import { adminActionRateLimit } from '@/middleware/rateLimit';
import {
  InviteCreateSchema,
  InviteUpdateSchema,
  PaginationSchema,
  IdParamSchema,
} from '@/schemas/auth';
import {
  InviteCreateInput,
  InviteUpdateInput,
  PaginationQuery,
} from '@/types';
import { v4 as uuidv4 } from 'uuid';

export async function inviteRoutes(fastify: FastifyInstance) {
  // 获取邀请码列表
  fastify.get<{ Querystring: PaginationQuery }>('/', {
    schema: PaginationSchema,
    preHandler: [fastify.authenticate, requireAdmin()],
    handler: async (request: FastifyRequest<{ Querystring: PaginationQuery }>, reply: FastifyReply) => {
      try {
        const {
          page = 1,
          limit = 20,
          search,
          status,
          sortBy = 'createdAt',
          sortOrder = 'desc',
        } = request.query;

        const skip = (page - 1) * limit;
        const where: any = {};

        // 搜索条件
        if (search) {
          where.OR = [
            { code: { contains: search } },
            { description: { contains: search } },
          ];
        }

        if (status === 'active') {
          where.isActive = true;
          where.expireAt = { gt: new Date() };
          where.OR = [
            { maxUsage: { gt: { usageCount: 0 } } },
            { maxUsage: null },
          ];
        } else if (status === 'inactive') {
          where.OR = [
            { isActive: false },
            { expireAt: { lte: new Date() } },
          ];
        }

        // 获取邀请码列表和总数
        const [invites, total] = await Promise.all([
          prisma.invite.findMany({
            where,
            skip,
            take: limit,
            orderBy: { [sortBy]: sortOrder },
            // 暂时不包含关系数据
          }),
          prisma.invite.count({ where }),
        ]);

        // 计算邀请码状态
        const invitesWithStatus = invites.map(invite => ({
          ...invite,
          usageCount: invite._count.users,
          status: getInviteStatus(invite, invite._count.users),
        }));

        return reply.code(200).send({
          success: true,
          data: {
            invites: invitesWithStatus,
            pagination: {
              page,
              limit,
              total,
              pages: Math.ceil(total / limit),
            },
          },
        });
      } catch (error) {
        logger.error('Get invites error:', error);
        return reply.code(500).send({
          success: false,
          message: '获取邀请码列表失败',
          error: 'INTERNAL_ERROR',
        });
      }
    },
  });

  // 获取单个邀请码详情
  fastify.get<{ Params: { id: string } }>('/:id', {
    schema: IdParamSchema,
    preHandler: [fastify.authenticate, requireAdmin()],
    handler: async (request: FastifyRequest<{ Params: { id: string } }>, reply: FastifyReply) => {
      try {
        const { id } = request.params;

        const invite = await prisma.invite.findUnique({
          where: { id },
          // 暂时不包含关系数据
        });

        if (!invite) {
          return reply.code(404).send({
            success: false,
            message: '邀请码不存在',
            error: 'INVITE_NOT_FOUND',
          });
        }

        const inviteWithStatus = {
          ...invite,
          usageCount: invite.users.length,
          status: getInviteStatus(invite, invite.users.length),
        };

        return reply.code(200).send({
          success: true,
          data: inviteWithStatus,
        });
      } catch (error) {
        logger.error('Get invite error:', error);
        return reply.code(500).send({
          success: false,
          message: '获取邀请码详情失败',
          error: 'INTERNAL_ERROR',
        });
      }
    },
  });

  // 创建邀请码
  fastify.post<{ Body: InviteCreateInput }>('/', {
    schema: InviteCreateSchema,
    preHandler: [fastify.authenticate, requireAdmin('admin'), adminActionRateLimit],
    handler: async (request: FastifyRequest<{ Body: InviteCreateInput }>, reply: FastifyReply) => {
      try {
        const inviteData = request.body;
        const admin = request.user as any;

        // 生成邀请码（如果未提供）
        const code = inviteData.code || generateInviteCode();

        // 检查邀请码是否已存在
        const existingInvite = await prisma.invite.findUnique({
          where: { code },
        });

        if (existingInvite) {
          return reply.code(409).send({
            success: false,
            message: '邀请码已存在',
            error: 'INVITE_CODE_EXISTS',
          });
        }

        // 创建邀请码
        const invite = await prisma.invite.create({
          data: {
            code,
            maxUsage: inviteData.maxUsage,
            expireAt: new Date(inviteData.expireAt),
            description: inviteData.description,
            creatorId: admin.adminId,
          },
          // 暂时不包含关系数据
        });

        // 记录审计日志
        await prisma.auditLog.create({
          data: {
            action: 'invite_created',
            details: {
              inviteId: invite.id,
              code: invite.code,
              maxUsage: invite.maxUsage,
              expireAt: invite.expireAt,
            },
            adminId: admin.adminId,
            ipAddress: request.ip,
            userAgent: request.headers['user-agent'] || '',
          },
        });

        SecurityLogger.logSecurityEvent(
          'invite_created',
          {
            inviteId: invite.id,
            code: invite.code,
            adminId: admin.adminId,
            ip: request.ip,
          },
          'low'
        );

        return reply.code(201).send({
          success: true,
          message: '邀请码创建成功',
          data: {
            ...invite,
            usageCount: 0,
            status: 'active',
          },
        });
      } catch (error) {
        logger.error('Create invite error:', error);
        return reply.code(500).send({
          success: false,
          message: '创建邀请码失败',
          error: 'INTERNAL_ERROR',
        });
      }
    },
  });

  // 更新邀请码
  fastify.put<{ Params: { id: string }; Body: InviteUpdateInput }>('/:id', {
    schema: InviteUpdateSchema,
    preHandler: [fastify.authenticate, requireAdmin('admin'), adminActionRateLimit],
    handler: async (request: FastifyRequest<{ Params: { id: string }; Body: InviteUpdateInput }>, reply: FastifyReply) => {
      try {
        const { id } = request.params;
        const updateData = request.body;
        const admin = request.user as any;

        // 检查邀请码是否存在
        const existingInvite = await prisma.invite.findUnique({
          where: { id },
        });

        if (!existingInvite) {
          return reply.code(404).send({
            success: false,
            message: '邀请码不存在',
            error: 'INVITE_NOT_FOUND',
          });
        }

        // 准备更新数据
        const updatePayload: any = {};

        if (updateData.maxUsage !== undefined) {
          updatePayload.maxUsage = updateData.maxUsage;
        }

        if (updateData.expireAt) {
          updatePayload.expireAt = new Date(updateData.expireAt);
        }

        if (updateData.description !== undefined) {
          updatePayload.description = updateData.description;
        }

        if (updateData.isActive !== undefined) {
          updatePayload.isActive = updateData.isActive;
        }

        // 更新邀请码
        const invite = await prisma.invite.update({
          where: { id },
          data: updatePayload,
          // 暂时不包含关系数据
        });

        // 记录审计日志
        await prisma.auditLog.create({
          data: {
            action: 'invite_updated',
            details: {
              inviteId: invite.id,
              code: invite.code,
              changes: updateData,
              oldData: {
                maxUsage: existingInvite.maxUsage,
                expireAt: existingInvite.expireAt,
                isActive: existingInvite.isActive,
              },
            },
            adminId: admin.adminId,
            ipAddress: request.ip,
            userAgent: request.headers['user-agent'] || '',
          },
        });

        SecurityLogger.logSecurityEvent(
          'invite_updated',
          {
            inviteId: invite.id,
            code: invite.code,
            adminId: admin.adminId,
            changes: Object.keys(updateData),
            ip: request.ip,
          },
          'low'
        );

        const inviteWithStatus = {
          ...invite,
          usageCount: invite._count.users,
          status: getInviteStatus(invite, invite._count.users),
        };

        return reply.code(200).send({
          success: true,
          message: '邀请码更新成功',
          data: inviteWithStatus,
        });
      } catch (error) {
        logger.error('Update invite error:', error);
        return reply.code(500).send({
          success: false,
          message: '更新邀请码失败',
          error: 'INTERNAL_ERROR',
        });
      }
    },
  });

  // 删除邀请码
  fastify.delete<{ Params: { id: string } }>('/:id', {
    schema: IdParamSchema,
    preHandler: [fastify.authenticate, requireAdmin('admin'), adminActionRateLimit],
    handler: async (request: FastifyRequest<{ Params: { id: string } }>, reply: FastifyReply) => {
      try {
        const { id } = request.params;
        const admin = request.user as any;

        // 检查邀请码是否存在
        const invite = await prisma.invite.findUnique({
          where: { id },
          include: {
            _count: {
              select: {
                users: true,
              },
            },
          },
        });

        if (!invite) {
          return reply.code(404).send({
            success: false,
            message: '邀请码不存在',
            error: 'INVITE_NOT_FOUND',
          });
        }

        // 检查是否有用户使用了此邀请码
        if (invite._count.users > 0) {
          return reply.code(400).send({
            success: false,
            message: '邀请码已被使用，无法删除',
            error: 'INVITE_IN_USE',
          });
        }

        // 删除邀请码
        await prisma.invite.delete({
          where: { id },
        });

        // 记录审计日志
        await prisma.auditLog.create({
          data: {
            action: 'invite_deleted',
            details: {
              inviteId: invite.id,
              code: invite.code,
              maxUsage: invite.maxUsage,
            },
            adminId: admin.adminId,
            ipAddress: request.ip,
            userAgent: request.headers['user-agent'] || '',
          },
        });

        SecurityLogger.logSecurityEvent(
          'invite_deleted',
          {
            inviteId: invite.id,
            code: invite.code,
            adminId: admin.adminId,
            ip: request.ip,
          },
          'medium'
        );

        return reply.code(200).send({
          success: true,
          message: '邀请码删除成功',
        });
      } catch (error) {
        logger.error('Delete invite error:', error);
        return reply.code(500).send({
          success: false,
          message: '删除邀请码失败',
          error: 'INTERNAL_ERROR',
        });
      }
    },
  });

  // 验证邀请码
  fastify.get<{ Params: { code: string } }>('/verify/:code', {
    handler: async (request: FastifyRequest<{ Params: { code: string } }>, reply: FastifyReply) => {
      try {
        const { code } = request.params;

        const invite = await prisma.invite.findUnique({
          where: { code },
          include: {
            _count: {
              select: {
                users: true,
              },
            },
          },
        });

        if (!invite) {
          return reply.code(404).send({
            success: false,
            message: '邀请码不存在',
            error: 'INVITE_NOT_FOUND',
          });
        }

        const status = getInviteStatus(invite, invite._count.users);

        if (status !== 'active') {
          return reply.code(400).send({
            success: false,
            message: '邀请码已失效',
            error: 'INVITE_EXPIRED',
            details: { status },
          });
        }

        return reply.code(200).send({
          success: true,
          message: '邀请码有效',
          data: {
            code: invite.code,
            description: invite.description,
            usageCount: invite._count.users,
            maxUsage: invite.maxUsage,
            expireAt: invite.expireAt,
            status,
          },
        });
      } catch (error) {
        logger.error('Verify invite error:', error);
        return reply.code(500).send({
          success: false,
          message: '验证邀请码失败',
          error: 'INTERNAL_ERROR',
        });
      }
    },
  });
}

/**
 * 生成邀请码
 */
function generateInviteCode(): string {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
  let result = '';
  for (let i = 0; i < 8; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return result;
}

/**
 * 获取邀请码状态
 */
function getInviteStatus(invite: any, usageCount: number): string {
  if (!invite.isActive) {
    return 'disabled';
  }

  if (invite.expireAt && new Date() > invite.expireAt) {
    return 'expired';
  }

  if (invite.maxUsage && usageCount >= invite.maxUsage) {
    return 'exhausted';
  }

  return 'active';
}
