/**
 * 认证相关的TypeBox验证模式
 */

import { Type } from '@sinclair/typebox';

// 用户认证输入验证
export const AuthenticateUserSchema = {
  body: Type.Object({
    qq: Type.String({ minLength: 5, maxLength: 12, pattern: '^\\d+$' }),
    verificationData: Type.Object({
      category: Type.Union([
        Type.Literal('本校'),
        Type.Literal('新生'),
        Type.Literal('外校'),
        Type.Literal('其他')
      ]),
      realName: Type.String({ minLength: 1, maxLength: 100 }),
      school: Type.Optional(Type.String()),
      studentId: Type.Optional(Type.String()),
      email: Type.Optional(Type.String({ format: 'email' })),
      phone: Type.Optional(Type.String({ pattern: '^1[3-9]\\d{9}$' })),
      department: Type.Optional(Type.String()),
      grade: Type.Optional(Type.String()),
      inviteCode: Type.Optional(Type.String()),
      uploadedImages: Type.Optional(Type.Array(Type.String())),
      extraInfo: Type.Optional(Type.Record(Type.String(), Type.Any())),
    }),
  }),
};

// 管理员登录输入验证
export const AdminLoginSchema = {
  body: Type.Object({
    username: Type.String({ minLength: 1, maxLength: 50 }),
    password: Type.String({ minLength: 1 }),
    totpCode: Type.Optional(Type.String({ minLength: 6, maxLength: 6, pattern: '^\\d+$' })),
  }),
};

// 验证码发送输入验证
export const VerificationCodeSchema = {
  body: Type.Object({
    target: Type.String({ minLength: 1 }),
    type: Type.Union([Type.Literal('email'), Type.Literal('sms')]),
    purpose: Type.Optional(Type.Union([
      Type.Literal('verification'),
      Type.Literal('registration'),
      Type.Literal('login'),
      Type.Literal('password_reset'),
      Type.Literal('email_change')
    ])),
  }),
};

// 验证码验证输入验证
export const VerificationCodeVerifySchema = {
  body: Type.Object({
    target: Type.String({ minLength: 1 }),
    code: Type.String({ minLength: 4, maxLength: 8, pattern: '^\\d+$' }),
    type: Type.Union([Type.Literal('email'), Type.Literal('sms')]),
    purpose: Type.Optional(Type.Union([
      Type.Literal('verification'),
      Type.Literal('registration'),
      Type.Literal('login'),
      Type.Literal('password_reset'),
      Type.Literal('email_change')
    ])),
  }),
};

// 刷新令牌输入验证
export const RefreshTokenSchema = {
  body: Type.Object({
    refreshToken: Type.String({ minLength: 1 }),
  }),
};

// 用户创建输入验证
export const UserCreateSchema = {
  body: Type.Object({
    qq: Type.String({ minLength: 5, maxLength: 12, pattern: '^\\d+$' }),
    realName: Type.String({ minLength: 1, maxLength: 100 }),
    school: Type.String({ minLength: 1, maxLength: 200 }),
    studentId: Type.Optional(Type.String({ maxLength: 50 })),
    email: Type.Optional(Type.String({ format: 'email' })),
    phone: Type.Optional(Type.String({ pattern: '^1[3-9]\\d{9}$' })),
    category: Type.Union([
      Type.Literal('本校'),
      Type.Literal('新生'),
      Type.Literal('外校'),
      Type.Literal('其他')
    ]),
    department: Type.Optional(Type.String({ maxLength: 100 })),
    grade: Type.Optional(Type.String({ maxLength: 20 })),
    status: Type.Optional(Type.Union([
      Type.Literal('active'),
      Type.Literal('inactive'),
      Type.Literal('suspended')
    ])),
    extraInfo: Type.Optional(Type.Record(Type.String(), Type.Any())),
  }),
};

// 用户更新输入验证
export const UserUpdateSchema = {
  body: Type.Object({
    realName: Type.Optional(Type.String({ minLength: 1, maxLength: 100 })),
    school: Type.Optional(Type.String({ minLength: 1, maxLength: 200 })),
    studentId: Type.Optional(Type.String({ maxLength: 50 })),
    email: Type.Optional(Type.String({ format: 'email' })),
    phone: Type.Optional(Type.String({ pattern: '^1[3-9]\\d{9}$' })),
    department: Type.Optional(Type.String({ maxLength: 100 })),
    grade: Type.Optional(Type.String({ maxLength: 20 })),
    status: Type.Optional(Type.Union([
      Type.Literal('active'),
      Type.Literal('inactive'),
      Type.Literal('suspended')
    ])),
    extraInfo: Type.Optional(Type.Record(Type.String(), Type.Any())),
  }),
  params: Type.Object({
    id: Type.String({ format: 'uuid' }),
  }),
};

// 待审核用户创建输入验证
export const PendingUserCreateSchema = {
  body: Type.Object({
    qq: Type.String({ minLength: 5, maxLength: 12, pattern: '^\\d+$' }),
    realName: Type.String({ minLength: 1, maxLength: 100 }),
    school: Type.String({ minLength: 1, maxLength: 200 }),
    studentId: Type.Optional(Type.String({ maxLength: 50 })),
    email: Type.Optional(Type.String({ format: 'email' })),
    phone: Type.Optional(Type.String({ pattern: '^1[3-9]\\d{9}$' })),
    category: Type.Union([
      Type.Literal('新生'),
      Type.Literal('外校')
    ]),
    department: Type.Optional(Type.String({ maxLength: 100 })),
    grade: Type.Optional(Type.String({ maxLength: 20 })),
    uploadedImages: Type.Optional(Type.Array(Type.String())),
    extraInfo: Type.Optional(Type.Record(Type.String(), Type.Any())),
  }),
};

// 待审核用户审核输入验证
export const PendingUserAuditSchema = {
  body: Type.Object({
    approved: Type.Boolean(),
    reason: Type.Optional(Type.String({ maxLength: 500 })),
  }),
  params: Type.Object({
    id: Type.String({ format: 'uuid' }),
  }),
};

// 管理员创建输入验证
export const AdminCreateSchema = {
  body: Type.Object({
    username: Type.String({ minLength: 3, maxLength: 50, pattern: '^[a-zA-Z0-9_]+$' }),
    password: Type.String({ minLength: 8, maxLength: 128 }),
    role: Type.Union([
      Type.Literal('admin'),
      Type.Literal('auditor'),
      Type.Literal('staff')
    ]),
    email: Type.Optional(Type.String({ format: 'email' })),
    phone: Type.Optional(Type.String({ pattern: '^1[3-9]\\d{9}$' })),
    realName: Type.Optional(Type.String({ minLength: 1, maxLength: 100 })),
  }),
};

// 管理员更新输入验证
export const AdminUpdateSchema = {
  body: Type.Object({
    password: Type.Optional(Type.String({ minLength: 8, maxLength: 128 })),
    role: Type.Optional(Type.Union([
      Type.Literal('admin'),
      Type.Literal('auditor'),
      Type.Literal('staff')
    ])),
    email: Type.Optional(Type.String({ format: 'email' })),
    phone: Type.Optional(Type.String({ pattern: '^1[3-9]\\d{9}$' })),
    realName: Type.Optional(Type.String({ minLength: 1, maxLength: 100 })),
    isActive: Type.Optional(Type.Boolean()),
  }),
  params: Type.Object({
    id: Type.String({ format: 'uuid' }),
  }),
};

// 配置更新输入验证
export const ConfigUpdateSchema = {
  body: Type.Object({
    configs: Type.Array(Type.Object({
      key: Type.String({ minLength: 1 }),
      value: Type.Any(),
      description: Type.Optional(Type.String()),
    })),
  }),
};

// 邀请码创建输入验证
export const InviteCreateSchema = {
  body: Type.Object({
    code: Type.Optional(Type.String({ minLength: 6, maxLength: 20 })),
    maxUsage: Type.Integer({ minimum: 1, maximum: 1000 }),
    expireAt: Type.String({ format: 'date-time' }),
    description: Type.Optional(Type.String({ maxLength: 200 })),
  }),
};

// 邀请码更新输入验证
export const InviteUpdateSchema = {
  body: Type.Object({
    maxUsage: Type.Optional(Type.Integer({ minimum: 1, maximum: 1000 })),
    expireAt: Type.Optional(Type.String({ format: 'date-time' })),
    description: Type.Optional(Type.String({ maxLength: 200 })),
    isActive: Type.Optional(Type.Boolean()),
  }),
  params: Type.Object({
    id: Type.String({ format: 'uuid' }),
  }),
};

// 分页查询参数验证
export const PaginationSchema = {
  querystring: Type.Object({
    page: Type.Optional(Type.String({ pattern: '^\\d+$' })),
    limit: Type.Optional(Type.String({ pattern: '^\\d+$' })),
    search: Type.Optional(Type.String()),
    category: Type.Optional(Type.String()),
    status: Type.Optional(Type.String()),
    sortBy: Type.Optional(Type.String()),
    sortOrder: Type.Optional(Type.Union([
      Type.Literal('asc'),
      Type.Literal('desc')
    ])),
  }),
};

// ID参数验证
export const IdParamSchema = {
  params: Type.Object({
    id: Type.String({ format: 'uuid' }),
  }),
};

// 批量操作输入验证
export const BatchOperationSchema = {
  body: Type.Object({
    ids: Type.Array(Type.String({ format: 'uuid' }), { minItems: 1, maxItems: 100 }),
    action: Type.String({ minLength: 1 }),
    data: Type.Optional(Type.Record(Type.String(), Type.Any())),
  }),
};

// 文件上传验证
export const FileUploadSchema = {
  body: Type.Object({
    purpose: Type.Union([
      Type.Literal('avatar'),
      Type.Literal('document'),
      Type.Literal('verification')
    ]),
    category: Type.Optional(Type.String()),
  }),
};

// 审计日志查询验证
export const AuditLogQuerySchema = {
  querystring: Type.Object({
    page: Type.Optional(Type.String({ pattern: '^\\d+$' })),
    limit: Type.Optional(Type.String({ pattern: '^\\d+$' })),
    action: Type.Optional(Type.String()),
    userId: Type.Optional(Type.String({ format: 'uuid' })),
    adminId: Type.Optional(Type.String({ format: 'uuid' })),
    startDate: Type.Optional(Type.String({ format: 'date-time' })),
    endDate: Type.Optional(Type.String({ format: 'date-time' })),
    ipAddress: Type.Optional(Type.String()),
  }),
};

// 统计查询验证
export const StatisticsQuerySchema = {
  querystring: Type.Object({
    startDate: Type.Optional(Type.String({ format: 'date-time' })),
    endDate: Type.Optional(Type.String({ format: 'date-time' })),
    groupBy: Type.Optional(Type.Union([
      Type.Literal('day'),
      Type.Literal('week'),
      Type.Literal('month')
    ])),
    category: Type.Optional(Type.String()),
  }),
};

// 导出验证
export const ExportSchema = {
  querystring: Type.Object({
    format: Type.Optional(Type.Union([
      Type.Literal('csv'),
      Type.Literal('xlsx'),
      Type.Literal('json')
    ])),
    category: Type.Optional(Type.String()),
    status: Type.Optional(Type.String()),
    startDate: Type.Optional(Type.String({ format: 'date-time' })),
    endDate: Type.Optional(Type.String({ format: 'date-time' })),
  }),
};

// 密码重置验证
export const PasswordResetSchema = {
  body: Type.Object({
    email: Type.String({ format: 'email' }),
  }),
};

// 密码重置确认验证
export const PasswordResetConfirmSchema = {
  body: Type.Object({
    token: Type.String({ minLength: 1 }),
    newPassword: Type.String({ minLength: 8, maxLength: 128 }),
  }),
};

// 密码修改验证
export const PasswordChangeSchema = {
  body: Type.Object({
    currentPassword: Type.String({ minLength: 1 }),
    newPassword: Type.String({ minLength: 8, maxLength: 128 }),
  }),
};
