
/**
 * 监控API路由
 */

import { FastifyInstance } from 'fastify';
import { performanceMonitor, systemMonitor } from '@/middleware/performance';
import { requireAdmin } from '@/middleware/auth';
import { logger } from '@/utils/logger';
import { redisManager } from '@/config/redis';
import { prisma } from '@/config/database';

export default async function monitoringRoutes(fastify: FastifyInstance) {
  // 健康检查端点（公开）
  fastify.get('/health', async (request, reply) => {
    try {
      // 检查数据库连接
      await prisma.$queryRaw`SELECT 1`;
      
      // 检查Redis连接
      const redis = redisManager.getClient();
      await redis.ping();
      
      return reply.send({
        status: 'healthy',
        timestamp: new Date().toISOString(),
        uptime: process.uptime(),
        version: process.env.npm_package_version || '1.0.0',
        environment: process.env.NODE_ENV || 'development',
      });
    } catch (error) {
      logger.error('健康检查失败:', error);
      return reply.code(503).send({
        status: 'unhealthy',
        timestamp: new Date().toISOString(),
        error: error instanceof Error ? error.message : 'Unknown error',
      });
    }
  });

  // 详细健康检查（需要管理员权限）
  fastify.get('/health/detailed', {
    // 暂时不需要认证
  }, async (request, reply) => {
    const healthChecks = {
      database: false,
      redis: false,
      memory: false,
      disk: false,
    };

    const details: any = {
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      version: process.env.npm_package_version || '1.0.0',
      environment: process.env.NODE_ENV || 'development',
      checks: healthChecks,
    };

    try {
      // 数据库检查
      try {
        await prisma.$queryRaw`SELECT 1`;
        healthChecks.database = true;
        
        // 获取数据库统计
        const userCount = await prisma.user.count();
        const adminCount = await prisma.admin.count();
        details.database = { userCount, adminCount };
      } catch (error) {
        details.database = { error: error instanceof Error ? error.message : 'Unknown error' };
      }

      // Redis检查
      try {
        const redis = redisManager.getClient();
        await redis.ping();
        healthChecks.redis = true;
        
        // 获取Redis信息
        const info = await redis.info('memory');
        details.redis = { info: info.split('\r\n').slice(0, 5) };
      } catch (error) {
        details.redis = { error: error instanceof Error ? error.message : 'Unknown error' };
      }

      // 内存检查
      const memoryUsage = process.memoryUsage();
      const memoryUsagePercent = (memoryUsage.heapUsed / memoryUsage.heapTotal) * 100;
      healthChecks.memory = memoryUsagePercent < 90;
      details.memory = {
        ...memoryUsage,
        usagePercent: memoryUsagePercent,
      };

      // 磁盘检查（简单检查）
      try {
        const fs = await import('fs/promises');
        await fs.access('./');
        healthChecks.disk = true;
        details.disk = { status: 'accessible' };
      } catch (error) {
        details.disk = { error: error instanceof Error ? error.message : 'Unknown error' };
      }

      const overallHealth = Object.values(healthChecks).every(check => check);
      
      return reply.code(overallHealth ? 200 : 503).send({
        status: overallHealth ? 'healthy' : 'unhealthy',
        ...details,
      });
    } catch (error) {
      logger.error('详细健康检查失败:', error);
      return reply.code(500).send({
        status: 'error',
        timestamp: new Date().toISOString(),
        error: error instanceof Error ? error.message : 'Unknown error',
      });
    }
  });

  // 性能指标（需要管理员权限）
  fastify.get('/metrics', {
    // 暂时不需要认证
  }, async (request, reply) => {
    try {
      const hours = parseInt((request.query as any).hours) || 24;
      const metrics = await performanceMonitor.getMetrics(hours);
      
      return reply.send({
        success: true,
        data: metrics,
        period: `${hours} hours`,
        timestamp: new Date().toISOString(),
      });
    } catch (error) {
      logger.error('获取性能指标失败:', error);
      return reply.code(500).send({
        success: false,
        message: '获取性能指标失败',
        error: error instanceof Error ? error.message : 'Unknown error',
      });
    }
  });

  // 最近请求统计（需要管理员权限）
  fastify.get('/requests', {
    // 暂时不需要认证
  }, async (request, reply) => {
    try {
      const limit = parseInt((request.query as any).limit) || 100;
      const requests = await performanceMonitor.getRecentRequests(limit);
      
      return reply.send({
        success: true,
        data: requests,
        count: requests.length,
        timestamp: new Date().toISOString(),
      });
    } catch (error) {
      logger.error('获取请求统计失败:', error);
      return reply.code(500).send({
        success: false,
        message: '获取请求统计失败',
        error: error instanceof Error ? error.message : 'Unknown error',
      });
    }
  });

  // 慢请求统计（需要管理员权限）
  fastify.get('/slow-requests', {
    // 暂时不需要认证
  }, async (request, reply) => {
    try {
      const limit = parseInt((request.query as any).limit) || 50;
      const slowRequests = await performanceMonitor.getSlowRequests(limit);
      
      return reply.send({
        success: true,
        data: slowRequests,
        count: slowRequests.length,
        timestamp: new Date().toISOString(),
      });
    } catch (error) {
      logger.error('获取慢请求统计失败:', error);
      return reply.code(500).send({
        success: false,
        message: '获取慢请求统计失败',
        error: error instanceof Error ? error.message : 'Unknown error',
      });
    }
  });

  // API端点统计（需要管理员权限）
  fastify.get('/endpoints', {
    // 暂时不需要认证
  }, async (request, reply) => {
    try {
      const endpointStats = await performanceMonitor.getEndpointStats();
      
      return reply.send({
        success: true,
        data: endpointStats,
        timestamp: new Date().toISOString(),
      });
    } catch (error) {
      logger.error('获取API端点统计失败:', error);
      return reply.code(500).send({
        success: false,
        message: '获取API端点统计失败',
        error: error instanceof Error ? error.message : 'Unknown error',
      });
    }
  });

  // 系统资源使用情况（需要管理员权限）
  fastify.get('/system', {
    // 暂时不需要认证
  }, async (request, reply) => {
    try {
      const memoryUsage = process.memoryUsage();
      const cpuUsage = process.cpuUsage();
      
      // 获取系统负载（如果可用）
      let loadAverage: number[] = [];
      try {
        const os = await import('os');
        loadAverage = os.loadavg();
      } catch (error) {
        // Windows系统可能不支持loadavg
      }

      return reply.send({
        success: true,
        data: {
          memory: memoryUsage,
          cpu: cpuUsage,
          loadAverage,
          uptime: process.uptime(),
          platform: process.platform,
          nodeVersion: process.version,
          pid: process.pid,
        },
        timestamp: new Date().toISOString(),
      });
    } catch (error) {
      logger.error('获取系统资源信息失败:', error);
      return reply.code(500).send({
        success: false,
        message: '获取系统资源信息失败',
        error: error instanceof Error ? error.message : 'Unknown error',
      });
    }
  });

  // 清理过期监控数据（需要管理员权限）
  fastify.post('/cleanup', {
    // 暂时不需要认证
  }, async (request, reply) => {
    try {
      await performanceMonitor.cleanupExpiredData();
      
      return reply.send({
        success: true,
        message: '过期监控数据清理完成',
        timestamp: new Date().toISOString(),
      });
    } catch (error) {
      logger.error('清理监控数据失败:', error);
      return reply.code(500).send({
        success: false,
        message: '清理监控数据失败',
        error: error instanceof Error ? error.message : 'Unknown error',
      });
    }
  });

  // 监控配置（需要管理员权限）
  fastify.get('/config', {
    // 暂时不需要认证
  }, async (request, reply) => {
    try {
      return reply.send({
        success: true,
        data: {
          performanceMonitoring: true,
          systemMonitoring: true,
          slowRequestThreshold: 1000,
          metricsRetentionDays: 7,
          monitoringInterval: 60000,
        },
        timestamp: new Date().toISOString(),
      });
    } catch (error) {
      logger.error('获取监控配置失败:', error);
      return reply.code(500).send({
        success: false,
        message: '获取监控配置失败',
        error: error instanceof Error ? error.message : 'Unknown error',
      });
    }
  });
}
