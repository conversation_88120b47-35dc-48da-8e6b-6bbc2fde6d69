import axios from 'axios'
import { ElMessage, ElMessageBox } from 'element-plus'
import { getToken, removeToken } from './auth'
import router from '@/router'

// 创建axios实例
const service = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL || '/api',
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json;charset=UTF-8'
  }
})

// 请求拦截器
service.interceptors.request.use(
  config => {
    // 添加认证token
    const token = getToken()
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
      console.log(`[Token] 添加Authorization header: Bearer ${token.substring(0, 20)}...`)
    } else {
      console.log('[Token] 没有找到token，跳过Authorization header')
    }

    // 添加CSRF令牌（对于POST、PUT、DELETE、PATCH请求）
    if (['post', 'put', 'delete', 'patch'].includes(config.method)) {
      const csrfToken = getCsrfToken()
      if (csrfToken) {
        config.headers['X-CSRF-Token'] = csrfToken
      }
    }

    // 添加请求ID用于追踪
    config.headers['X-Request-ID'] = generateRequestId()

    // 记录请求日志
    console.log(`[API请求] ${config.method?.toUpperCase()} ${config.url}`, {
      params: config.params,
      data: config.data
    })

    return config
  },
  error => {
    console.error('[请求拦截器错误]', error)
    return Promise.reject(error)
  }
)

// 响应拦截器
service.interceptors.response.use(
  response => {
    const res = response.data

    // 记录响应日志
    console.log(`[API响应] ${response.config.method?.toUpperCase()} ${response.config.url}`, {
      status: response.status,
      data: res
    })

    // 检查业务状态码
    if (res.success === false) {
      // 处理业务错误
      const errorMessage = res.message || '请求失败'
      
      // 特殊错误码处理
      if (res.code === 'UNAUTHORIZED') {
        handleUnauthorized()
        return Promise.reject(new Error(errorMessage))
      }
      
      if (res.code === 'FORBIDDEN') {
        ElMessage.error('权限不足')
        return Promise.reject(new Error(errorMessage))
      }
      
      if (res.code === 'RATE_LIMITED') {
        ElMessage.warning('请求过于频繁，请稍后再试')
        return Promise.reject(new Error(errorMessage))
      }
      
      if (res.code === 'MAINTENANCE') {
        ElMessage.warning('系统维护中，请稍后再试')
        return Promise.reject(new Error(errorMessage))
      }

      // 其他业务错误 - 不在这里显示消息，让调用方处理
      return Promise.reject(res)
    }

    return res
  },
  error => {
    console.error('[API响应错误]', error)

    let message = '网络错误'
    
    if (error.response) {
      const { status, data } = error.response
      
      switch (status) {
        case 400:
          // 400错误不在这里显示消息，让调用方处理
          // 确保错误对象包含完整的响应数据
          if (data) {
            error.response.data = data
            // 如果后端返回了具体的错误信息，更新error.message
            if (data.message) {
              error.message = data.message
            }
          }
          return Promise.reject(error)
        case 401:
          // 对于401错误，优先使用后端返回的错误信息
          if (data && data.message) {
            message = data.message
            // 如果是登录相关的错误（用户名密码错误等），不执行handleUnauthorized
            if (data.error === 'INVALID_CREDENTIALS' || data.message.includes('用户名') || data.message.includes('密码')) {
              // 不执行handleUnauthorized，让调用方处理
              error.response.data = data
              return Promise.reject(error)
            }
          } else {
            message = '认证失败，请重新登录'
          }
          handleUnauthorized()
          break
        case 403:
          message = '权限不足'
          break
        case 404:
          message = '请求的资源不存在'
          break
        case 429:
          message = '请求过于频繁，请稍后再试'
          break
        case 500:
          message = '服务器内部错误'
          break
        case 502:
          message = '网关错误'
          break
        case 503:
          message = '服务暂时不可用'
          break
        case 504:
          message = '网关超时'
          break
        default:
          message = data?.message || `请求失败 (${status})`
      }
    } else if (error.request) {
      message = '网络连接失败，请检查网络设置'
    } else {
      message = error.message || '请求配置错误'
    }

    ElMessage.error(message)
    return Promise.reject(error)
  }
)

// 处理未授权
function handleUnauthorized() {
  removeToken()
  
  ElMessageBox.confirm(
    '登录状态已过期，请重新登录',
    '提示',
    {
      confirmButtonText: '重新登录',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(() => {
    // 跳转到登录页
    const currentPath = router.currentRoute.value.fullPath
    const isAdminPath = currentPath.startsWith('/admin')
    
    router.push({
      name: isAdminPath ? 'AdminLogin' : 'Login',
      query: { redirect: currentPath }
    })
  }).catch(() => {
    // 用户取消，跳转到首页
    router.push({ name: 'Home' })
  })
}

// 生成请求ID
function generateRequestId() {
  return Date.now().toString(36) + Math.random().toString(36).substr(2)
}

// 获取CSRF令牌
function getCsrfToken() {
  return document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || 
         sessionStorage.getItem('csrf-token')
}

// 设置CSRF令牌
export function setCsrfToken(token) {
  sessionStorage.setItem('csrf-token', token)
}

// 封装常用请求方法
export const request = {
  get(url, params = {}) {
    return service.get(url, { params })
  },

  post(url, data = {}) {
    return service.post(url, data)
  },

  put(url, data = {}) {
    return service.put(url, data)
  },

  patch(url, data = {}) {
    return service.patch(url, data)
  },

  delete(url, params = {}) {
    return service.delete(url, { params })
  },

  upload(url, formData, onProgress) {
    return service.post(url, formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      },
      onUploadProgress: onProgress
    })
  },

  download(url, params = {}, filename) {
    return service.get(url, {
      params,
      responseType: 'blob'
    }).then(response => {
      const blob = new Blob([response.data])
      const downloadUrl = window.URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = downloadUrl
      link.download = filename || 'download'
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      window.URL.revokeObjectURL(downloadUrl)
    })
  }
}

// 设置axios配置
export function setupAxios() {
  // 设置默认配置
  axios.defaults.timeout = 30000
  axios.defaults.headers.common['X-Requested-With'] = 'XMLHttpRequest'
  
  // 添加全局错误处理
  window.addEventListener('unhandledrejection', event => {
    if (event.reason?.isAxiosError) {
      console.error('未处理的axios错误:', event.reason)
    }
  })
}

export default service