// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// 基础模型 - 软删除混入
model User {
  id        String   @id @default(uuid()) @db.Uuid
  createdAt DateTime @default(now()) @map("created_at") @db.Timestamptz
  updatedAt DateTime @updatedAt @map("updated_at") @db.Timestamptz
  deleted   Boolean  @default(false)

  // 用户基本信息
  qq        String  @unique @db.VarChar(20)
  realName  String  @map("real_name") @db.VarChar(100)
  school    String  @db.VarChar(200)
  studentId String? @map("student_id") @db.VarChar(50)
  email     String? @db.VarChar(255)
  phone     String? @db.VarChar(20)
  category  String  @db.VarChar(20) // 本校/新生/外校/其他
  extraInfo Json    @default("{}") @map("extra_info") @db.JsonB

  // 技术文档要求的字段
  department   String? @db.VarChar(200) // 院系
  grade        String? @db.VarChar(20) // 年级
  idCardType   String  @default("identity") @map("id_card_type") @db.VarChar(20)
  idCardNumber String? @map("id_card_number") @db.Text // 加密存储
  status       String  @default("active") @db.VarChar(20)
  remark       String? @db.Text

  @@map("users")
}

model PendingUser {
  id        String   @id @default(uuid()) @db.Uuid
  createdAt DateTime @default(now()) @map("created_at") @db.Timestamptz
  updatedAt DateTime @updatedAt @map("updated_at") @db.Timestamptz
  deleted   Boolean  @default(false)

  // 用户基本信息
  qq             String    @db.VarChar(20)
  realName       String    @map("real_name") @db.VarChar(100)
  school         String    @db.VarChar(200)
  studentId      String?   @map("student_id") @db.VarChar(50)
  email          String?   @db.VarChar(255)
  phone          String?   @db.VarChar(20)
  category       String    @db.VarChar(20)
  uploadedImages Json      @default("[]") @map("uploaded_images") @db.JsonB
  extraInfo      Json      @default("{}") @map("extra_info") @db.JsonB
  status         String    @default("pending") @db.VarChar(20) // pending/approved/rejected
  auditLog       String?   @map("audit_log") @db.Text
  auditorId      String?   @map("auditor_id") @db.Uuid
  auditedAt      DateTime? @map("audited_at") @db.Timestamptz

  // 技术文档要求的字段
  department   String? @db.VarChar(200)
  grade        String? @db.VarChar(20)
  idCardType   String  @default("identity") @map("id_card_type") @db.VarChar(20)
  idCardNumber String? @map("id_card_number") @db.Text

  // 关系
  auditor Admin? @relation(fields: [auditorId], references: [id])

  @@map("pending_users")
}

model Admin {
  id        String   @id @default(uuid()) @db.Uuid
  createdAt DateTime @default(now()) @map("created_at") @db.Timestamptz
  updatedAt DateTime @updatedAt @map("updated_at") @db.Timestamptz

  username            String    @unique @db.VarChar(50)
  passwordHash        String    @map("password_hash") @db.VarChar(255)
  email               String    @db.VarChar(255)
  role                String    @default("admin") @db.VarChar(20) // admin/auditor/staff
  totpSecret          String?   @map("totp_secret") @db.VarChar(32)
  isActive            Boolean   @default(true) @map("is_active")
  lastLogin           DateTime? @map("last_login") @db.Timestamptz
  failedLoginAttempts Int       @default(0) @map("failed_login_attempts")
  lockedUntil         DateTime? @map("locked_until") @db.Timestamptz
  lastLoginAt         DateTime? @map("last_login_at") @db.Timestamptz
  lastLoginIp         String?   @map("last_login_ip") @db.VarChar(45)

  // 关系
  auditedUsers PendingUser[]
  auditLogs    AuditLog[]

  @@map("admins")
}

model Config {
  key         String   @id @db.VarChar(100)
  value       Json     @db.JsonB
  description String?  @db.Text
  createdAt   DateTime @default(now()) @map("created_at") @db.Timestamptz
  updatedAt   DateTime @updatedAt @map("updated_at") @db.Timestamptz

  @@map("configs")
}

model AuditLog {
  id        String   @id @default(uuid()) @db.Uuid
  createdAt DateTime @default(now()) @map("created_at") @db.Timestamptz

  action       String  @db.VarChar(100)
  resourceType String  @map("resource_type") @db.VarChar(50)
  resourceId   String? @map("resource_id") @db.VarChar(100)
  userId       String? @map("user_id") @db.Uuid
  adminId      String? @map("admin_id") @db.Uuid
  ipAddress    String? @map("ip_address") @db.VarChar(45)
  userAgent    String? @map("user_agent") @db.Text
  details      Json    @default("{}") @db.JsonB

  // 关系
  admin Admin? @relation(fields: [adminId], references: [id])

  @@map("audit_logs")
}

model Invite {
  id        String   @id @default(uuid()) @db.Uuid
  createdAt DateTime @default(now()) @map("created_at") @db.Timestamptz
  updatedAt DateTime @updatedAt @map("updated_at") @db.Timestamptz

  code      String   @unique @db.VarChar(50)
  creatorId String   @map("creator_id") @db.Uuid
  maxUsage  Int      @default(1) @map("max_usage")
  used      Int      @default(0)
  expireAt  DateTime @map("expire_at") @db.Timestamptz
  isActive  Boolean  @default(true) @map("is_active")

  @@map("invites")
}

model VerificationCode {
  id        String   @id @default(uuid()) @db.Uuid
  createdAt DateTime @default(now()) @map("created_at") @db.Timestamptz

  target   String   @db.VarChar(255) // 邮箱或手机号
  code     String   @db.VarChar(10)
  type     String   @db.VarChar(20) // email/sms
  purpose  String   @db.VarChar(50) // verification/reset/etc
  expireAt DateTime @map("expire_at") @db.Timestamptz
  used     Boolean  @default(false)
  attempts Int      @default(0)

  @@map("verification_codes")
}
