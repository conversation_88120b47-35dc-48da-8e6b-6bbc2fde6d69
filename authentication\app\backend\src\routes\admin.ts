/**
 * 管理员路由
 */

import { FastifyInstance, FastifyRequest, FastifyReply } from 'fastify';
import { prisma } from '@/config/database';
import { logger, SecurityLogger } from '@/utils/logger';
import { requireAdmin } from '@/middleware/auth';
import { adminActionRateLimit } from '@/middleware/rateLimit';
import {
  AdminCreateSchema,
  AdminUpdateSchema,
  AdminLoginSchema,
  PaginationSchema,
  IdParamSchema,
  PasswordChangeSchema,
} from '@/schemas/auth';
import {
  AdminCreateInput,
  AdminUpdateInput,
  AdminLoginInput,
  PaginationQuery,
  PasswordChangeInput,
} from '@/types';
import bcrypt from 'bcrypt';

export async function adminRoutes(fastify: FastifyInstance) {
  // 管理员登录
  fastify.post<{ Body: AdminLoginInput }>('/login', {
    schema: AdminLoginSchema,
    handler: async (request: FastifyRequest<{ Body: AdminLoginInput }>, reply: FastifyReply) => {
      try {
        const loginData = request.body;
        const ipAddress = request.ip;
        const userAgent = request.headers['user-agent'] || '';

        // 这里需要导入authService
        const { authService } = await import('@/services/AuthService');
        const result = await authService.authenticateAdmin(loginData, ipAddress, userAgent);

        if (result.success) {
          return reply.code(200).send(result);
        } else {
          return reply.code(401).send(result);
        }
      } catch (error) {
        logger.error('Admin login error:', error);
        return reply.code(500).send({
          success: false,
          message: '登录过程中发生错误',
          error: 'INTERNAL_ERROR',
        });
      }
    },
  });

  // 获取管理员列表
  fastify.get<{ Querystring: PaginationQuery }>('/', {
    schema: PaginationSchema,
    preHandler: [fastify.authenticate],
    handler: async (request: FastifyRequest<{ Querystring: PaginationQuery }>, reply: FastifyReply) => {
      try {
        const {
          page = 1,
          limit = 20,
          search,
          sortBy = 'createdAt',
          sortOrder = 'desc',
        } = request.query;

        const skip = (page - 1) * limit;
        const where: any = {
          isActive: true,
        };

        // 搜索条件
        if (search) {
          where.OR = [
            { username: { contains: search } },
            { realName: { contains: search } },
            { email: { contains: search } },
          ];
        }

        // 获取管理员列表和总数
        const [admins, total] = await Promise.all([
          prisma.admin.findMany({
            where,
            skip,
            take: limit,
            orderBy: { [sortBy]: sortOrder },
            select: {
              id: true,
              username: true,
              role: true,
              email: true,
              phone: true,
              realName: true,
              lastLoginAt: true,
              loginAttempts: true,
              lockedUntil: true,
              totpEnabled: true,
              createdAt: true,
              updatedAt: true,
            },
          }),
          prisma.admin.count({ where }),
        ]);

        return reply.code(200).send({
          success: true,
          data: {
            admins,
            pagination: {
              page,
              limit,
              total,
              pages: Math.ceil(total / limit),
            },
          },
        });
      } catch (error) {
        logger.error('Get admins error:', error);
        return reply.code(500).send({
          success: false,
          message: '获取管理员列表失败',
          error: 'INTERNAL_ERROR',
        });
      }
    },
  });

  // 获取单个管理员详情
  fastify.get<{ Params: { id: string } }>('/:id', {
    schema: IdParamSchema,
    preHandler: [fastify.authenticate, requireAdmin('admin')],
    handler: async (request: FastifyRequest<{ Params: { id: string } }>, reply: FastifyReply) => {
      try {
        const { id } = request.params;

        const admin = await prisma.admin.findFirst({
          where: {
            id,
            isActive: true,
          },
          select: {
            id: true,
            username: true,
            role: true,
            email: true,
            phone: true,
            realName: true,
            lastLoginAt: true,
            loginAttempts: true,
            lockedUntil: true,
            totpEnabled: true,
            createdAt: true,
            updatedAt: true,
            auditLogs: {
              take: 20,
              orderBy: { createdAt: 'desc' },
              select: {
                id: true,
                action: true,
                details: true,
                ipAddress: true,
                userAgent: true,
                createdAt: true,
              },
            },
          },
        });

        if (!admin) {
          return reply.code(404).send({
            success: false,
            message: '管理员不存在',
            error: 'ADMIN_NOT_FOUND',
          });
        }

        return reply.code(200).send({
          success: true,
          data: admin,
        });
      } catch (error) {
        logger.error('Get admin error:', error);
        return reply.code(500).send({
          success: false,
          message: '获取管理员详情失败',
          error: 'INTERNAL_ERROR',
        });
      }
    },
  });

  // 创建管理员
  fastify.post<{ Body: AdminCreateInput }>('/', {
    schema: AdminCreateSchema,
    preHandler: [fastify.authenticate, requireAdmin('admin'), adminActionRateLimit],
    handler: async (request: FastifyRequest<{ Body: AdminCreateInput }>, reply: FastifyReply) => {
      try {
        const adminData = request.body;
        const currentAdmin = request.user as any;

        // 检查用户名是否已存在
        const existingAdmin = await prisma.admin.findFirst({
          where: {
            username: adminData.username,
            isActive: true,
          },
        });

        if (existingAdmin) {
          return reply.code(409).send({
            success: false,
            message: '用户名已存在',
            error: 'USERNAME_EXISTS',
          });
        }

        // 检查邮箱是否已存在（如果提供）
        if (adminData.email) {
          const existingEmail = await prisma.admin.findFirst({
            where: {
              email: adminData.email,
              isActive: true,
            },
          });

          if (existingEmail) {
            return reply.code(409).send({
              success: false,
              message: '邮箱已存在',
              error: 'EMAIL_EXISTS',
            });
          }
        }

        // 加密密码
        const saltRounds = parseInt(process.env.BCRYPT_ROUNDS || '12');
        const hashedPassword = await bcrypt.hash(adminData.password, saltRounds);

        // 创建管理员
        const admin = await prisma.admin.create({
          data: {
            username: adminData.username,
            password: hashedPassword,
            role: adminData.role,
            email: adminData.email,
            phone: adminData.phone,
            realName: adminData.realName,
          },
          select: {
            id: true,
            username: true,
            role: true,
            email: true,
            phone: true,
            realName: true,
            createdAt: true,
          },
        });

        // 记录审计日志
        await prisma.auditLog.create({
          data: {
            action: 'admin_created',
            details: {
              adminId: admin.id,
              username: admin.username,
              role: admin.role,
            },
            adminId: currentAdmin.adminId,
            ipAddress: request.ip,
            userAgent: request.headers['user-agent'] || '',
          },
        });

        SecurityLogger.logSecurityEvent(
          'admin_created',
          {
            newAdminId: admin.id,
            username: admin.username,
            role: admin.role,
            createdBy: currentAdmin.adminId,
            ip: request.ip,
          },
          'medium'
        );

        return reply.code(201).send({
          success: true,
          message: '管理员创建成功',
          data: admin,
        });
      } catch (error) {
        logger.error('Create admin error:', error);
        return reply.code(500).send({
          success: false,
          message: '创建管理员失败',
          error: 'INTERNAL_ERROR',
        });
      }
    },
  });

  // 更新管理员
  fastify.put<{ Params: { id: string }; Body: AdminUpdateInput }>('/:id', {
    schema: AdminUpdateSchema,
    preHandler: [fastify.authenticate, requireAdmin('admin'), adminActionRateLimit],
    handler: async (request: FastifyRequest<{ Params: { id: string }; Body: AdminUpdateInput }>, reply: FastifyReply) => {
      try {
        const { id } = request.params;
        const updateData = request.body;
        const currentAdmin = request.user as any;

        // 检查管理员是否存在
        const existingAdmin = await prisma.admin.findFirst({
          where: {
            id,
            isActive: true,
          },
        });

        if (!existingAdmin) {
          return reply.code(404).send({
            success: false,
            message: '管理员不存在',
            error: 'ADMIN_NOT_FOUND',
          });
        }

        // 不能修改自己的角色
        if (id === currentAdmin.adminId && updateData.role) {
          return reply.code(403).send({
            success: false,
            message: '不能修改自己的角色',
            error: 'CANNOT_MODIFY_OWN_ROLE',
          });
        }

        // 检查邮箱是否已被其他管理员使用
        if (updateData.email && updateData.email !== existingAdmin.email) {
          const emailExists = await prisma.admin.findFirst({
            where: {
              email: updateData.email,
              isActive: true,
              id: { not: id },
            },
          });

          if (emailExists) {
            return reply.code(409).send({
              success: false,
              message: '邮箱已被其他管理员使用',
              error: 'EMAIL_EXISTS',
            });
          }
        }

        // 准备更新数据
        const updatePayload: any = { ...updateData };

        // 如果更新密码，需要加密
        if (updateData.password) {
          const saltRounds = parseInt(process.env.BCRYPT_ROUNDS || '12');
          updatePayload.password = await bcrypt.hash(updateData.password, saltRounds);
        }

        // 更新管理员
        const admin = await prisma.admin.update({
          where: { id },
          data: updatePayload,
          select: {
            id: true,
            username: true,
            role: true,
            email: true,
            phone: true,
            realName: true,
            isActive: true,
            updatedAt: true,
          },
        });

        // 记录审计日志
        await prisma.auditLog.create({
          data: {
            action: 'admin_updated',
            details: {
              adminId: admin.id,
              changes: Object.keys(updateData),
              oldData: {
                role: existingAdmin.role,
                email: existingAdmin.email,
                isActive: existingAdmin.isActive,
              },
            },
            adminId: currentAdmin.adminId,
            ipAddress: request.ip,
            userAgent: request.headers['user-agent'] || '',
          },
        });

        SecurityLogger.logSecurityEvent(
          'admin_updated',
          {
            adminId: admin.id,
            updatedBy: currentAdmin.adminId,
            changes: Object.keys(updateData),
            ip: request.ip,
          },
          'medium'
        );

        return reply.code(200).send({
          success: true,
          message: '管理员更新成功',
          data: admin,
        });
      } catch (error) {
        logger.error('Update admin error:', error);
        return reply.code(500).send({
          success: false,
          message: '更新管理员失败',
          error: 'INTERNAL_ERROR',
        });
      }
    },
  });

  // 删除管理员（软删除）
  fastify.delete<{ Params: { id: string } }>('/:id', {
    schema: IdParamSchema,
    preHandler: [fastify.authenticate, requireAdmin('admin'), adminActionRateLimit],
    handler: async (request: FastifyRequest<{ Params: { id: string } }>, reply: FastifyReply) => {
      try {
        const { id } = request.params;
        const currentAdmin = request.user as any;

        // 不能删除自己
        if (id === currentAdmin.adminId) {
          return reply.code(403).send({
            success: false,
            message: '不能删除自己',
            error: 'CANNOT_DELETE_SELF',
          });
        }

        // 检查管理员是否存在
        const admin = await prisma.admin.findFirst({
          where: {
            id,
            isActive: true,
          },
        });

        if (!admin) {
          return reply.code(404).send({
            success: false,
            message: '管理员不存在',
            error: 'ADMIN_NOT_FOUND',
          });
        }

        // 软删除管理员
        await prisma.admin.update({
          where: { id },
          data: { isActive: false },
        });

        // 记录审计日志
        await prisma.auditLog.create({
          data: {
            action: 'admin_deleted',
            details: {
              adminId: admin.id,
              username: admin.username,
              role: admin.role,
            },
            adminId: currentAdmin.adminId,
            ipAddress: request.ip,
            userAgent: request.headers['user-agent'] || '',
          },
        });

        SecurityLogger.logSecurityEvent(
          'admin_deleted',
          {
            adminId: admin.id,
            username: admin.username,
            deletedBy: currentAdmin.adminId,
            ip: request.ip,
          },
          'high'
        );

        return reply.code(200).send({
          success: true,
          message: '管理员删除成功',
        });
      } catch (error) {
        logger.error('Delete admin error:', error);
        return reply.code(500).send({
          success: false,
          message: '删除管理员失败',
          error: 'INTERNAL_ERROR',
        });
      }
    },
  });

  // 修改密码
  fastify.post<{ Body: PasswordChangeInput }>('/change-password', {
    schema: PasswordChangeSchema,
    preHandler: [fastify.authenticate],
    handler: async (request: FastifyRequest<{ Body: PasswordChangeInput }>, reply: FastifyReply) => {
      try {
        const { currentPassword, newPassword } = request.body;
        const admin = request.user as any;

        // 获取管理员信息
        const adminData = await prisma.admin.findUnique({
          where: { id: admin.adminId },
        });

        if (!adminData) {
          return reply.code(404).send({
            success: false,
            message: '管理员不存在',
            error: 'ADMIN_NOT_FOUND',
          });
        }

        // 验证当前密码
        const isCurrentPasswordValid = await bcrypt.compare(currentPassword, adminData.password);
        if (!isCurrentPasswordValid) {
          return reply.code(400).send({
            success: false,
            message: '当前密码错误',
            error: 'INVALID_CURRENT_PASSWORD',
          });
        }

        // 加密新密码
        const saltRounds = parseInt(process.env.BCRYPT_ROUNDS || '12');
        const hashedNewPassword = await bcrypt.hash(newPassword, saltRounds);

        // 更新密码
        await prisma.admin.update({
          where: { id: admin.adminId },
          data: { password: hashedNewPassword },
        });

        // 记录审计日志
        await prisma.auditLog.create({
          data: {
            action: 'password_changed',
            details: {
              adminId: admin.adminId,
            },
            adminId: admin.adminId,
            ipAddress: request.ip,
            userAgent: request.headers['user-agent'] || '',
          },
        });

        SecurityLogger.logSecurityEvent(
          'password_changed',
          {
            adminId: admin.adminId,
            ip: request.ip,
          },
          'medium'
        );

        return reply.code(200).send({
          success: true,
          message: '密码修改成功',
        });
      } catch (error) {
        logger.error('Change password error:', error);
        return reply.code(500).send({
          success: false,
          message: '修改密码失败',
          error: 'INTERNAL_ERROR',
        });
      }
    },
  });

  // 用户管理相关API (临时添加到admin路由中)

  // 获取用户列表
  fastify.get('/users', {
    preHandler: [fastify.authenticate],
    handler: async (request: FastifyRequest, reply: FastifyReply) => {
      try {
        const users = await prisma.user.findMany({
          where: { deleted: false },
          select: {
            id: true,
            qq: true,
            realName: true,
            category: true,
            createdAt: true,
            updatedAt: true,
          },
          orderBy: { createdAt: 'desc' },
          take: 20,
        });

        return reply.code(200).send({
          success: true,
          data: users,
          pagination: {
            page: 1,
            per_page: 20,
            total: users.length,
            total_pages: 1,
          },
        });
      } catch (error) {
        logger.error('Get users error:', error);
        return reply.code(500).send({
          success: false,
          message: '获取用户列表失败',
          error: 'INTERNAL_ERROR',
        });
      }
    },
  });

  // 获取待审核用户列表
  fastify.get('/pending', {
    preHandler: [fastify.authenticate],
    handler: async (request: FastifyRequest, reply: FastifyReply) => {
      try {
        const pendingUsers = await prisma.pendingUser.findMany({
          where: { deleted: false },
          select: {
            id: true,
            qq: true,
            realName: true,
            category: true,
            status: true,
            createdAt: true,
            updatedAt: true,
          },
          orderBy: { createdAt: 'desc' },
          take: 20,
        });

        return reply.code(200).send({
          success: true,
          data: pendingUsers,
          pagination: {
            page: 1,
            per_page: 20,
            total: pendingUsers.length,
            total_pages: 1,
          },
        });
      } catch (error) {
        logger.error('Get pending users error:', error);
        return reply.code(500).send({
          success: false,
          message: '获取待审核用户列表失败',
          error: 'INTERNAL_ERROR',
        });
      }
    },
  });

  // 获取仪表盘统计数据
  fastify.get('/dashboard/stats', {
    preHandler: [fastify.authenticate],
    handler: async (request: FastifyRequest, reply: FastifyReply) => {
      try {
        const [userCount, pendingCount, todayUsers] = await Promise.all([
          prisma.user.count({ where: { deleted: false } }),
          prisma.pendingUser.count({ where: { deleted: false, status: 'pending' } }),
          prisma.user.count({
            where: {
              deleted: false,
              createdAt: {
                gte: new Date(new Date().setHours(0, 0, 0, 0)),
              },
            },
          }),
        ]);

        return reply.code(200).send({
          success: true,
          data: {
            total_users: userCount,
            pending_users: pendingCount,
            today_users: todayUsers,
            week_users: todayUsers, // 简化统计
          },
        });
      } catch (error) {
        logger.error('Get dashboard stats error:', error);
        return reply.code(500).send({
          success: false,
          message: '获取统计数据失败',
          error: 'INTERNAL_ERROR',
        });
      }
    },
  });
}
