/**
 * 待审核用户路由
 */

import { FastifyInstance, FastifyRequest, FastifyReply } from 'fastify';
import { prisma } from '@/config/database';
import { logger, SecurityLogger } from '@/utils/logger';
import { requireAdmin } from '@/middleware/auth';
import { adminActionRateLimit } from '@/middleware/rateLimit';
import { emailService } from '@/services/EmailService';
import {
  PendingUserCreateSchema,
  PendingUserAuditSchema,
  PaginationSchema,
  IdParamSchema,
  BatchOperationSchema,
} from '@/schemas/auth';
import {
  PendingUserCreateInput,
  PendingUserAuditInput,
  PaginationQuery,
  BatchOperationInput,
} from '@/types';

export async function pendingRoutes(fastify: FastifyInstance) {
  // 获取待审核用户列表
  fastify.get<{ Querystring: PaginationQuery }>('/', {
    schema: PaginationSchema,
    preHandler: [fastify.authenticate, requireAdmin()],
    handler: async (request: FastifyRequest<{ Querystring: PaginationQuery }>, reply: FastifyReply) => {
      try {
        const {
          page = 1,
          limit = 20,
          search,
          category,
          status,
          sortBy = 'createdAt',
          sortOrder = 'desc',
        } = request.query;

        const skip = (page - 1) * limit;
        const where: any = {
          deleted: false,
        };

        // 搜索条件
        if (search) {
          where.OR = [
            { qq: { contains: search } },
            { realName: { contains: search } },
            { school: { contains: search } },
            { email: { contains: search } },
          ];
        }

        if (category) {
          where.category = category;
        }

        if (status) {
          where.status = status;
        }

        // 获取待审核用户列表和总数
        const [pendingUsers, total] = await Promise.all([
          prisma.pendingUser.findMany({
            where,
            skip,
            take: limit,
            orderBy: { [sortBy]: sortOrder },
            select: {
              id: true,
              qq: true,
              realName: true,
              school: true,
              category: true,
              status: true,
              email: true,
              phone: true,
              department: true,
              grade: true,
              uploadedImages: true,
              auditLog: true,
              auditedAt: true,
              createdAt: true,
              updatedAt: true,
              auditor: {
                select: {
                  username: true,
                  email: true,
                },
              },
            },
          }),
          prisma.pendingUser.count({ where }),
        ]);

        return reply.code(200).send({
          success: true,
          data: {
            pendingUsers,
            pagination: {
              page,
              limit,
              total,
              pages: Math.ceil(total / limit),
            },
          },
        });
      } catch (error) {
        logger.error('Get pending users error:', error);
        return reply.code(500).send({
          success: false,
          message: '获取待审核用户列表失败',
          error: 'INTERNAL_ERROR',
        });
      }
    },
  });

  // 获取单个待审核用户详情
  fastify.get<{ Params: { id: string } }>('/:id', {
    schema: IdParamSchema,
    preHandler: [fastify.authenticate, requireAdmin()],
    handler: async (request: FastifyRequest<{ Params: { id: string } }>, reply: FastifyReply) => {
      try {
        const { id } = request.params;

        const pendingUser = await prisma.pendingUser.findFirst({
          where: {
            id,
            deleted: false,
          },
          include: {
            auditor: {
              select: {
                username: true,
                email: true,
              },
            },
          },
        });

        if (!pendingUser) {
          return reply.code(404).send({
            success: false,
            message: '待审核用户不存在',
            error: 'PENDING_USER_NOT_FOUND',
          });
        }

        return reply.code(200).send({
          success: true,
          data: pendingUser,
        });
      } catch (error) {
        logger.error('Get pending user error:', error);
        return reply.code(500).send({
          success: false,
          message: '获取待审核用户详情失败',
          error: 'INTERNAL_ERROR',
        });
      }
    },
  });

  // 创建待审核用户
  fastify.post<{ Body: PendingUserCreateInput }>('/', {
    schema: PendingUserCreateSchema,
    preHandler: [fastify.authenticate, requireAdmin('admin'), adminActionRateLimit],
    handler: async (request: FastifyRequest<{ Body: PendingUserCreateInput }>, reply: FastifyReply) => {
      try {
        const userData = request.body;
        const admin = request.user as any;

        // 检查QQ号是否已存在
        const [existingUser, existingPending] = await Promise.all([
          prisma.user.findFirst({
            where: {
              qq: userData.qq,
              deleted: false,
            },
          }),
          prisma.pendingUser.findFirst({
            where: {
              qq: userData.qq,
              deleted: false,
              status: 'pending',
            },
          }),
        ]);

        if (existingUser) {
          return reply.code(409).send({
            success: false,
            message: 'QQ号已存在于正式用户中',
            error: 'QQ_EXISTS_IN_USERS',
          });
        }

        if (existingPending) {
          return reply.code(409).send({
            success: false,
            message: 'QQ号已存在于待审核用户中',
            error: 'QQ_EXISTS_IN_PENDING',
          });
        }

        // 检查邮箱是否已存在（如果提供）
        if (userData.email) {
          const [existingUserEmail, existingPendingEmail] = await Promise.all([
            prisma.user.findFirst({
              where: {
                email: userData.email,
                deleted: false,
              },
            }),
            prisma.pendingUser.findFirst({
              where: {
                email: userData.email,
                deleted: false,
                status: 'pending',
              },
            }),
          ]);

          if (existingUserEmail || existingPendingEmail) {
            return reply.code(409).send({
              success: false,
              message: '邮箱已存在',
              error: 'EMAIL_EXISTS',
            });
          }
        }

        // 创建待审核用户
        const pendingUser = await prisma.pendingUser.create({
          data: userData,
        });

        // 记录审计日志
        await prisma.auditLog.create({
          data: {
            action: 'pending_user_created',
            details: {
              pendingUserId: pendingUser.id,
              qq: pendingUser.qq,
              realName: pendingUser.realName,
              category: pendingUser.category,
            },
            adminId: admin.adminId,
            ipAddress: request.ip,
            userAgent: request.headers['user-agent'] || '',
          },
        });

        SecurityLogger.logSecurityEvent(
          'pending_user_created',
          {
            pendingUserId: pendingUser.id,
            qq: pendingUser.qq,
            adminId: admin.adminId,
            ip: request.ip,
          },
          'low'
        );

        return reply.code(201).send({
          success: true,
          message: '待审核用户创建成功',
          data: {
            id: pendingUser.id,
            qq: pendingUser.qq,
            realName: pendingUser.realName,
            status: pendingUser.status,
          },
        });
      } catch (error) {
        logger.error('Create pending user error:', error);
        return reply.code(500).send({
          success: false,
          message: '创建待审核用户失败',
          error: 'INTERNAL_ERROR',
        });
      }
    },
  });

  // 审核用户
  fastify.post<{ Params: { id: string }; Body: PendingUserAuditInput }>('/:id/audit', {
    schema: PendingUserAuditSchema,
    preHandler: [fastify.authenticate, requireAdmin('auditor'), adminActionRateLimit],
    handler: async (request: FastifyRequest<{ Params: { id: string }; Body: PendingUserAuditInput }>, reply: FastifyReply) => {
      try {
        const { id } = request.params;
        const { approved, reason } = request.body;
        const admin = request.user as any;

        // 检查待审核用户是否存在
        const pendingUser = await prisma.pendingUser.findFirst({
          where: {
            id,
            deleted: false,
            status: 'pending',
          },
        });

        if (!pendingUser) {
          return reply.code(404).send({
            success: false,
            message: '待审核用户不存在或已处理',
            error: 'PENDING_USER_NOT_FOUND',
          });
        }

        let result;

        if (approved) {
          // 审核通过，创建正式用户
          result = await prisma.$transaction(async (tx) => {
            // 创建正式用户
            const user = await tx.user.create({
              data: {
                qq: pendingUser.qq,
                realName: pendingUser.realName,
                school: pendingUser.school,
                studentId: pendingUser.studentId,
                email: pendingUser.email,
                phone: pendingUser.phone,
                category: pendingUser.category,
                department: pendingUser.department,
                grade: pendingUser.grade,
                extraInfo: pendingUser.extraInfo,
                status: 'active',
              },
            });

            // 更新待审核用户状态
            await tx.pendingUser.update({
              where: { id },
              data: {
                status: 'approved',
                auditLog: reason,
                auditedAt: new Date(),
                auditorId: admin.adminId,
              },
            });

            return user;
          });

          // 发送审核通过邮件
          if (pendingUser.email) {
            try {
              await emailService.sendAuditResult(
                pendingUser.email,
                pendingUser.realName,
                true,
                reason
              );
            } catch (emailError) {
              logger.error('Send approval email error:', emailError);
            }
          }

          SecurityLogger.logSecurityEvent(
            'pending_user_approved',
            {
              pendingUserId: pendingUser.id,
              newUserId: result.id,
              qq: pendingUser.qq,
              adminId: admin.adminId,
              ip: request.ip,
            },
            'medium'
          );
        } else {
          // 审核拒绝
          await prisma.pendingUser.update({
            where: { id },
            data: {
              status: 'rejected',
              auditLog: reason,
              auditedAt: new Date(),
              auditorId: admin.adminId,
            },
          });

          // 发送审核拒绝邮件
          if (pendingUser.email) {
            try {
              await emailService.sendAuditResult(
                pendingUser.email,
                pendingUser.realName,
                false,
                reason
              );
            } catch (emailError) {
              logger.error('Send rejection email error:', emailError);
            }
          }

          SecurityLogger.logSecurityEvent(
            'pending_user_rejected',
            {
              pendingUserId: pendingUser.id,
              qq: pendingUser.qq,
              reason,
              adminId: admin.adminId,
              ip: request.ip,
            },
            'medium'
          );
        }

        // 记录审计日志
        await prisma.auditLog.create({
          data: {
            action: approved ? 'pending_user_approved' : 'pending_user_rejected',
            details: {
              pendingUserId: pendingUser.id,
              qq: pendingUser.qq,
              realName: pendingUser.realName,
              reason,
              newUserId: approved ? result?.id : undefined,
            },
            adminId: admin.adminId,
            ipAddress: request.ip,
            userAgent: request.headers['user-agent'] || '',
          },
        });

        return reply.code(200).send({
          success: true,
          message: approved ? '用户审核通过' : '用户审核拒绝',
          data: {
            status: approved ? 'approved' : 'rejected',
            newUserId: approved ? result?.id : undefined,
          },
        });
      } catch (error) {
        logger.error('Audit pending user error:', error);
        return reply.code(500).send({
          success: false,
          message: '审核用户失败',
          error: 'INTERNAL_ERROR',
        });
      }
    },
  });

  // 批量审核
  fastify.post<{ Body: BatchOperationInput }>('/batch-audit', {
    schema: BatchOperationSchema,
    preHandler: [fastify.authenticate, requireAdmin('auditor'), adminActionRateLimit],
    handler: async (request: FastifyRequest<{ Body: BatchOperationInput }>, reply: FastifyReply) => {
      try {
        const { ids, action, data } = request.body;
        const admin = request.user as any;

        if (!['approve', 'reject'].includes(action)) {
          return reply.code(400).send({
            success: false,
            message: '不支持的批量操作',
            error: 'UNSUPPORTED_ACTION',
          });
        }

        const approved = action === 'approve';
        const reason = data?.reason || '';

        // 获取待审核用户
        const pendingUsers = await prisma.pendingUser.findMany({
          where: {
            id: { in: ids },
            deleted: false,
            status: 'pending',
          },
        });

        if (pendingUsers.length === 0) {
          return reply.code(404).send({
            success: false,
            message: '没有找到可处理的待审核用户',
            error: 'NO_PENDING_USERS_FOUND',
          });
        }

        let successCount = 0;
        const results = [];

        for (const pendingUser of pendingUsers) {
          try {
            if (approved) {
              // 审核通过
              const result = await prisma.$transaction(async (tx) => {
                const user = await tx.user.create({
                  data: {
                    qq: pendingUser.qq,
                    realName: pendingUser.realName,
                    school: pendingUser.school,
                    studentId: pendingUser.studentId,
                    email: pendingUser.email,
                    phone: pendingUser.phone,
                    category: pendingUser.category,
                    department: pendingUser.department,
                    grade: pendingUser.grade,
                    extraInfo: pendingUser.extraInfo,
                    status: 'active',
                  },
                });

                await tx.pendingUser.update({
                  where: { id: pendingUser.id },
                  data: {
                    status: 'approved',
                    auditLog: reason,
                    auditedAt: new Date(),
                    auditorId: admin.adminId,
                  },
                });

                return user;
              });

              results.push({ id: pendingUser.id, success: true, newUserId: result.id });
            } else {
              // 审核拒绝
              await prisma.pendingUser.update({
                where: { id: pendingUser.id },
                data: {
                  status: 'rejected',
                  auditLog: reason,
                  auditedAt: new Date(),
                  auditorId: admin.adminId,
                },
              });

              results.push({ id: pendingUser.id, success: true });
            }

            // 发送邮件通知
            if (pendingUser.email) {
              try {
                await emailService.sendAuditResult(
                  pendingUser.email,
                  pendingUser.realName,
                  approved,
                  reason
                );
              } catch (emailError) {
                logger.error('Send audit email error:', emailError);
              }
            }

            successCount++;
          } catch (error) {
            logger.error(`Batch audit error for user ${pendingUser.id}:`, error);
            results.push({ id: pendingUser.id, success: false, error: error.message });
          }
        }

        // 记录审计日志
        await prisma.auditLog.create({
          data: {
            action: `batch_${action}`,
            details: {
              pendingUserIds: ids,
              successCount,
              totalCount: pendingUsers.length,
              reason,
              results,
            },
            adminId: admin.adminId,
            ipAddress: request.ip,
            userAgent: request.headers['user-agent'] || '',
          },
        });

        SecurityLogger.logSecurityEvent(
          `batch_${action}`,
          {
            pendingUserIds: ids,
            successCount,
            totalCount: pendingUsers.length,
            adminId: admin.adminId,
            ip: request.ip,
          },
          'medium'
        );

        return reply.code(200).send({
          success: true,
          message: `批量${approved ? '通过' : '拒绝'}完成`,
          data: {
            successCount,
            totalCount: pendingUsers.length,
            results,
          },
        });
      } catch (error) {
        logger.error('Batch audit error:', error);
        return reply.code(500).send({
          success: false,
          message: '批量审核失败',
          error: 'INTERNAL_ERROR',
        });
      }
    },
  });

  // 删除待审核用户
  fastify.delete<{ Params: { id: string } }>('/:id', {
    schema: IdParamSchema,
    preHandler: [fastify.authenticate, requireAdmin('admin'), adminActionRateLimit],
    handler: async (request: FastifyRequest<{ Params: { id: string } }>, reply: FastifyReply) => {
      try {
        const { id } = request.params;
        const admin = request.user as any;

        // 检查待审核用户是否存在
        const pendingUser = await prisma.pendingUser.findFirst({
          where: {
            id,
            deleted: false,
          },
        });

        if (!pendingUser) {
          return reply.code(404).send({
            success: false,
            message: '待审核用户不存在',
            error: 'PENDING_USER_NOT_FOUND',
          });
        }

        // 软删除待审核用户
        await prisma.pendingUser.update({
          where: { id },
          data: { deleted: true },
        });

        // 记录审计日志
        await prisma.auditLog.create({
          data: {
            action: 'pending_user_deleted',
            details: {
              pendingUserId: pendingUser.id,
              qq: pendingUser.qq,
              realName: pendingUser.realName,
              status: pendingUser.status,
            },
            adminId: admin.adminId,
            ipAddress: request.ip,
            userAgent: request.headers['user-agent'] || '',
          },
        });

        SecurityLogger.logSecurityEvent(
          'pending_user_deleted',
          {
            pendingUserId: pendingUser.id,
            qq: pendingUser.qq,
            adminId: admin.adminId,
            ip: request.ip,
          },
          'medium'
        );

        return reply.code(200).send({
          success: true,
          message: '待审核用户删除成功',
        });
      } catch (error) {
        logger.error('Delete pending user error:', error);
        return reply.code(500).send({
          success: false,
          message: '删除待审核用户失败',
          error: 'INTERNAL_ERROR',
        });
      }
    },
  });
}
