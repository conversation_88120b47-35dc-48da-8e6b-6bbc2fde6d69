import type { EnvConfig } from '@/types'

// 环境变量配置
export function getEnvConfig(): EnvConfig {
  return {
    // 应用配置
    APP_NAME: process.env.NEXT_PUBLIC_APP_NAME || '北航QQ身份认证系统',
    APP_VERSION: process.env.NEXT_PUBLIC_APP_VERSION || '1.0.0',
    APP_DESCRIPTION: process.env.NEXT_PUBLIC_APP_DESCRIPTION || '北京航空航天大学QQ身份认证系统',

    // API配置
    API_BASE_URL: import.meta.env.VITE_API_BASE_URL || '/api',
    API_TIMEOUT: parseInt(import.meta.env.VITE_API_TIMEOUT || '30000'),

    // 认证配置
    JWT_STORAGE_KEY: process.env.NEXT_PUBLIC_JWT_STORAGE_KEY || 'buaa_auth_token',
    REFRESH_TOKEN_KEY: process.env.NEXT_PUBLIC_REFRESH_TOKEN_KEY || 'buaa_refresh_token',
    TOKEN_EXPIRE_TIME: parseInt(process.env.NEXT_PUBLIC_TOKEN_EXPIRE_TIME || '3600'),

    // 功能开关
    ENABLE_REGISTRATION: process.env.NEXT_PUBLIC_ENABLE_REGISTRATION === 'true',
    ENABLE_SSO_AUTH: process.env.NEXT_PUBLIC_ENABLE_SSO_AUTH !== 'false',
    ENABLE_FRESHMAN_AUTH: process.env.NEXT_PUBLIC_ENABLE_FRESHMAN_AUTH !== 'false',
    ENABLE_EXTERNAL_AUTH: process.env.NEXT_PUBLIC_ENABLE_EXTERNAL_AUTH !== 'false',
    ENABLE_INVITE_AUTH: process.env.NEXT_PUBLIC_ENABLE_INVITE_AUTH !== 'false',

    // 文件上传配置
    MAX_FILE_SIZE: parseInt(process.env.NEXT_PUBLIC_MAX_FILE_SIZE || '16777216'),
    ALLOWED_FILE_TYPES: process.env.NEXT_PUBLIC_ALLOWED_FILE_TYPES || 'jpg,jpeg,png,pdf,webp',
    UPLOAD_ENDPOINT: process.env.NEXT_PUBLIC_UPLOAD_ENDPOINT || '/api/upload',

    // 验证码配置
    CAPTCHA_ENABLED: process.env.NEXT_PUBLIC_CAPTCHA_ENABLED !== 'false',
    SMS_COOLDOWN: parseInt(process.env.NEXT_PUBLIC_SMS_COOLDOWN || '60'),
    EMAIL_COOLDOWN: parseInt(process.env.NEXT_PUBLIC_EMAIL_COOLDOWN || '60'),

    // 安全配置
    CSRF_ENABLED: process.env.NEXT_PUBLIC_CSRF_ENABLED !== 'false',
    RATE_LIMIT_ENABLED: process.env.NEXT_PUBLIC_RATE_LIMIT_ENABLED !== 'false',
    SESSION_TIMEOUT: parseInt(process.env.NEXT_PUBLIC_SESSION_TIMEOUT || '3600'),

    // 主题配置
    DEFAULT_THEME: process.env.NEXT_PUBLIC_DEFAULT_THEME || 'light',
    ENABLE_DARK_MODE: process.env.NEXT_PUBLIC_ENABLE_DARK_MODE !== 'false',
    THEME_STORAGE_KEY: process.env.NEXT_PUBLIC_THEME_STORAGE_KEY || 'buaa_theme',

    // 语言配置
    DEFAULT_LOCALE: process.env.NEXT_PUBLIC_DEFAULT_LOCALE || 'zh-CN',
    SUPPORTED_LOCALES: process.env.NEXT_PUBLIC_SUPPORTED_LOCALES || 'zh-CN,en',

    // 分析和监控
    ENABLE_ANALYTICS: process.env.NEXT_PUBLIC_ENABLE_ANALYTICS === 'true',
    GOOGLE_ANALYTICS_ID: process.env.NEXT_PUBLIC_GOOGLE_ANALYTICS_ID || '',
    SENTRY_DSN: process.env.NEXT_PUBLIC_SENTRY_DSN || '',

    // 开发配置
    ENABLE_DEBUG: process.env.NEXT_PUBLIC_ENABLE_DEBUG === 'true',
    LOG_LEVEL: process.env.NEXT_PUBLIC_LOG_LEVEL || 'info',

    // 外部服务
    QQ_CONNECT_APP_ID: process.env.NEXT_PUBLIC_QQ_CONNECT_APP_ID || '',
    BUAA_SSO_URL: process.env.NEXT_PUBLIC_BUAA_SSO_URL || 'https://sso.buaa.edu.cn',

    // CDN配置
    CDN_BASE_URL: process.env.NEXT_PUBLIC_CDN_BASE_URL || '',
    STATIC_BASE_URL: process.env.NEXT_PUBLIC_STATIC_BASE_URL || '',

    // 缓存配置
    CACHE_ENABLED: process.env.NEXT_PUBLIC_CACHE_ENABLED !== 'false',
    CACHE_TTL: parseInt(process.env.NEXT_PUBLIC_CACHE_TTL || '300'),

    // 错误处理
    ERROR_REPORTING_ENABLED: process.env.NEXT_PUBLIC_ERROR_REPORTING_ENABLED === 'true',
    SHOW_ERROR_DETAILS: process.env.NEXT_PUBLIC_SHOW_ERROR_DETAILS === 'true',

    // 性能配置
    ENABLE_SW: process.env.NEXT_PUBLIC_ENABLE_SW !== 'false',
    ENABLE_PREFETCH: process.env.NEXT_PUBLIC_ENABLE_PREFETCH !== 'false',

    // 社交分享
    ENABLE_SOCIAL_SHARE: process.env.NEXT_PUBLIC_ENABLE_SOCIAL_SHARE === 'true',
    SITE_URL: process.env.NEXT_PUBLIC_SITE_URL || 'https://auth.buaa.edu.cn',

    // 联系信息
    SUPPORT_EMAIL: process.env.NEXT_PUBLIC_SUPPORT_EMAIL || '<EMAIL>',
    SUPPORT_PHONE: process.env.NEXT_PUBLIC_SUPPORT_PHONE || '010-82317114',

    // 法律信息
    PRIVACY_POLICY_URL: process.env.NEXT_PUBLIC_PRIVACY_POLICY_URL || '/privacy',
    TERMS_OF_SERVICE_URL: process.env.NEXT_PUBLIC_TERMS_OF_SERVICE_URL || '/terms',
    COPYRIGHT_YEAR: process.env.NEXT_PUBLIC_COPYRIGHT_YEAR || '2024',
    COPYRIGHT_HOLDER: process.env.NEXT_PUBLIC_COPYRIGHT_HOLDER || '北京航空航天大学',
  }
}

// 检查是否为开发环境
export function isDevelopment(): boolean {
  return process.env.NODE_ENV === 'development'
}

// 检查是否为生产环境
export function isProduction(): boolean {
  return process.env.NODE_ENV === 'production'
}

// 检查是否为测试环境
export function isTest(): boolean {
  return process.env.NODE_ENV === 'test'
}

// 检查是否在浏览器环境
export function isBrowser(): boolean {
  return typeof window !== 'undefined'
}

// 检查是否在服务器环境
export function isServer(): boolean {
  return typeof window === 'undefined'
}

// 获取基础URL
export function getBaseUrl(): string {
  if (isBrowser()) {
    return window.location.origin
  }
  
  // 服务器端
  const envConfig = getEnvConfig()
  return envConfig.SITE_URL || 'http://localhost:3000'
}

// 获取API基础URL
export function getApiBaseUrl(): string {
  const envConfig = getEnvConfig()
  return envConfig.API_BASE_URL
}

// 获取静态资源URL
export function getStaticUrl(path: string): string {
  const envConfig = getEnvConfig()
  const baseUrl = envConfig.STATIC_BASE_URL || envConfig.CDN_BASE_URL || getBaseUrl()
  return `${baseUrl}${path.startsWith('/') ? path : `/${path}`}`
}

// 获取上传文件URL
export function getUploadUrl(path: string): string {
  if (!path) return ''
  
  // 如果已经是完整URL，直接返回
  if (path.startsWith('http://') || path.startsWith('https://')) {
    return path
  }
  
  const apiBaseUrl = getApiBaseUrl()
  return `${apiBaseUrl}/file/${path.startsWith('/') ? path.slice(1) : path}`
}

// 检查功能是否启用
export function isFeatureEnabled(feature: keyof EnvConfig): boolean {
  const envConfig = getEnvConfig()
  return Boolean(envConfig[feature])
}

// 获取支持的语言列表
export function getSupportedLocales(): string[] {
  const envConfig = getEnvConfig()
  return envConfig.SUPPORTED_LOCALES.split(',').map(locale => locale.trim())
}

// 获取允许的文件类型列表
export function getAllowedFileTypes(): string[] {
  const envConfig = getEnvConfig()
  return envConfig.ALLOWED_FILE_TYPES.split(',').map(type => type.trim())
}

// 格式化文件大小
export function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 B'
  
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  
  return `${parseFloat((bytes / Math.pow(k, i)).toFixed(2))} ${sizes[i]}`
}

// 获取最大文件大小（格式化）
export function getMaxFileSizeFormatted(): string {
  const envConfig = getEnvConfig()
  return formatFileSize(envConfig.MAX_FILE_SIZE)
}

// 验证文件类型
export function isValidFileType(fileName: string): boolean {
  const allowedTypes = getAllowedFileTypes()
  const fileExtension = fileName.split('.').pop()?.toLowerCase()
  
  if (!fileExtension) return false
  
  return allowedTypes.includes(fileExtension)
}

// 验证文件大小
export function isValidFileSize(fileSize: number): boolean {
  const envConfig = getEnvConfig()
  return fileSize <= envConfig.MAX_FILE_SIZE
}

// 获取错误报告配置
export function getErrorReportingConfig() {
  const envConfig = getEnvConfig()
  return {
    enabled: envConfig.ERROR_REPORTING_ENABLED,
    showDetails: envConfig.SHOW_ERROR_DETAILS,
    sentryDsn: envConfig.SENTRY_DSN,
  }
}

// 获取分析配置
export function getAnalyticsConfig() {
  const envConfig = getEnvConfig()
  return {
    enabled: envConfig.ENABLE_ANALYTICS,
    googleAnalyticsId: envConfig.GOOGLE_ANALYTICS_ID,
  }
}
