/**
 * 文件上传路由
 */

import { FastifyInstance, FastifyRequest, FastifyReply } from 'fastify';
import { pipeline } from 'stream/promises';
import { createWriteStream } from 'fs';
import { mkdir, access, unlink } from 'fs/promises';
import { join, extname } from 'path';
import { v4 as uuidv4 } from 'uuid';
import { logger, SecurityLogger } from '@/utils/logger';
import { requireAdmin, optionalAuth } from '@/middleware/auth';
import { uploadRateLimit } from '@/middleware/rateLimit';

const UPLOAD_DIR = process.env.UPLOAD_DIR || './uploads';
const MAX_FILE_SIZE = parseInt(process.env.MAX_FILE_SIZE || '10485760'); // 10MB
const ALLOWED_EXTENSIONS = ['.jpg', '.jpeg', '.png', '.gif', '.pdf', '.doc', '.docx'];
const ALLOWED_MIME_TYPES = [
  'image/jpeg',
  'image/png',
  'image/gif',
  'application/pdf',
  'application/msword',
  'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
];

export async function uploadRoutes(fastify: FastifyInstance) {
  // 确保上传目录存在
  await ensureUploadDir();

  // 上传文件
  fastify.post('/file', {
    preHandler: [optionalAuth],
    handler: async (request: FastifyRequest, reply: FastifyReply) => {
      try {
        const data = await request.file();

        if (!data) {
          return reply.code(400).send({
            success: false,
            message: '没有上传文件',
            error: 'NO_FILE_UPLOADED',
          });
        }

        // 验证文件大小
        if (data.file.readableLength && data.file.readableLength > MAX_FILE_SIZE) {
          return reply.code(400).send({
            success: false,
            message: `文件大小超过限制 (${MAX_FILE_SIZE / 1024 / 1024}MB)`,
            error: 'FILE_TOO_LARGE',
          });
        }

        // 验证文件扩展名
        const fileExt = extname(data.filename).toLowerCase();
        if (!ALLOWED_EXTENSIONS.includes(fileExt)) {
          return reply.code(400).send({
            success: false,
            message: '不支持的文件类型',
            error: 'UNSUPPORTED_FILE_TYPE',
            allowedTypes: ALLOWED_EXTENSIONS,
          });
        }

        // 验证MIME类型
        if (!ALLOWED_MIME_TYPES.includes(data.mimetype)) {
          return reply.code(400).send({
            success: false,
            message: '不支持的文件格式',
            error: 'UNSUPPORTED_MIME_TYPE',
            allowedTypes: ALLOWED_MIME_TYPES,
          });
        }

        // 生成唯一文件名
        const fileId = uuidv4();
        const fileName = `${fileId}${fileExt}`;
        const filePath = join(UPLOAD_DIR, fileName);

        // 保存文件
        await pipeline(data.file, createWriteStream(filePath));

        // 获取文件信息
        const fileInfo = {
          id: fileId,
          originalName: data.filename,
          fileName,
          filePath,
          size: data.file.readableLength || 0,
          mimetype: data.mimetype,
          uploadedAt: new Date(),
        };

        // 记录上传日志
        const user = request.user as any;
        SecurityLogger.logSecurityEvent(
          'file_uploaded',
          {
            fileId,
            originalName: data.filename,
            size: fileInfo.size,
            mimetype: data.mimetype,
            userId: user?.userId,
            adminId: user?.adminId,
            ip: request.ip,
          },
          'low'
        );

        return reply.code(200).send({
          success: true,
          message: '文件上传成功',
          data: {
            fileId,
            originalName: data.filename,
            fileName,
            size: fileInfo.size,
            mimetype: data.mimetype,
            url: `/api/upload/file/${fileId}`,
          },
        });
      } catch (error) {
        logger.error('Upload file error:', error);
        return reply.code(500).send({
          success: false,
          message: '文件上传失败',
          error: 'UPLOAD_ERROR',
        });
      }
    },
  });

  // 批量上传文件
  fastify.post('/batch', {
    preHandler: [optionalAuth],
    handler: async (request: FastifyRequest, reply: FastifyReply) => {
      try {
        const files = request.files();
        const results = [];
        const errors = [];

        for await (const data of files) {
          try {
            // 验证文件
            const fileExt = extname(data.filename).toLowerCase();
            if (!ALLOWED_EXTENSIONS.includes(fileExt) || !ALLOWED_MIME_TYPES.includes(data.mimetype)) {
              errors.push({
                filename: data.filename,
                error: 'UNSUPPORTED_FILE_TYPE',
              });
              continue;
            }

            if (data.file.readableLength && data.file.readableLength > MAX_FILE_SIZE) {
              errors.push({
                filename: data.filename,
                error: 'FILE_TOO_LARGE',
              });
              continue;
            }

            // 保存文件
            const fileId = uuidv4();
            const fileName = `${fileId}${fileExt}`;
            const filePath = join(UPLOAD_DIR, fileName);

            await pipeline(data.file, createWriteStream(filePath));

            results.push({
              fileId,
              originalName: data.filename,
              fileName,
              size: data.file.readableLength || 0,
              mimetype: data.mimetype,
              url: `/api/upload/file/${fileId}`,
            });
          } catch (error) {
            logger.error(`Batch upload error for ${data.filename}:`, error);
            errors.push({
              filename: data.filename,
              error: 'UPLOAD_ERROR',
            });
          }
        }

        // 记录批量上传日志
        const user = request.user as any;
        SecurityLogger.logSecurityEvent(
          'batch_files_uploaded',
          {
            successCount: results.length,
            errorCount: errors.length,
            userId: user?.userId,
            adminId: user?.adminId,
            ip: request.ip,
          },
          'low'
        );

        return reply.code(200).send({
          success: true,
          message: '批量上传完成',
          data: {
            uploaded: results,
            errors,
            summary: {
              total: results.length + errors.length,
              success: results.length,
              failed: errors.length,
            },
          },
        });
      } catch (error) {
        logger.error('Batch upload error:', error);
        return reply.code(500).send({
          success: false,
          message: '批量上传失败',
          error: 'BATCH_UPLOAD_ERROR',
        });
      }
    },
  });

  // 获取文件
  fastify.get<{ Params: { fileId: string } }>('/file/:fileId', {
    handler: async (request: FastifyRequest<{ Params: { fileId: string } }>, reply: FastifyReply) => {
      try {
        const { fileId } = request.params;

        // 查找文件
        const files = await import('fs/promises');
        const uploadFiles = await files.readdir(UPLOAD_DIR);
        const targetFile = uploadFiles.find(file => file.startsWith(fileId));

        if (!targetFile) {
          return reply.code(404).send({
            success: false,
            message: '文件不存在',
            error: 'FILE_NOT_FOUND',
          });
        }

        const filePath = join(UPLOAD_DIR, targetFile);

        // 检查文件是否存在
        try {
          await access(filePath);
        } catch {
          return reply.code(404).send({
            success: false,
            message: '文件不存在',
            error: 'FILE_NOT_FOUND',
          });
        }

        // 获取文件扩展名并设置Content-Type
        const fileExt = extname(targetFile).toLowerCase();
        const mimeTypes: { [key: string]: string } = {
          '.jpg': 'image/jpeg',
          '.jpeg': 'image/jpeg',
          '.png': 'image/png',
          '.gif': 'image/gif',
          '.pdf': 'application/pdf',
          '.doc': 'application/msword',
          '.docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        };

        const contentType = mimeTypes[fileExt] || 'application/octet-stream';
        reply.header('Content-Type', contentType);

        // 如果是图片，设置缓存头
        if (contentType.startsWith('image/')) {
          reply.header('Cache-Control', 'public, max-age=86400'); // 1天
        }

        return reply.sendFile(targetFile, UPLOAD_DIR);
      } catch (error) {
        logger.error('Get file error:', error);
        return reply.code(500).send({
          success: false,
          message: '获取文件失败',
          error: 'GET_FILE_ERROR',
        });
      }
    },
  });

  // 删除文件（需要管理员权限）
  fastify.delete<{ Params: { fileId: string } }>('/file/:fileId', {
    preHandler: [fastify.authenticate, requireAdmin('admin')],
    handler: async (request: FastifyRequest<{ Params: { fileId: string } }>, reply: FastifyReply) => {
      try {
        const { fileId } = request.params;
        const admin = request.user as any;

        // 查找文件
        const files = await import('fs/promises');
        const uploadFiles = await files.readdir(UPLOAD_DIR);
        const targetFile = uploadFiles.find(file => file.startsWith(fileId));

        if (!targetFile) {
          return reply.code(404).send({
            success: false,
            message: '文件不存在',
            error: 'FILE_NOT_FOUND',
          });
        }

        const filePath = join(UPLOAD_DIR, targetFile);

        // 删除文件
        await unlink(filePath);

        // 记录删除日志
        SecurityLogger.logSecurityEvent(
          'file_deleted',
          {
            fileId,
            fileName: targetFile,
            adminId: admin.adminId,
            ip: request.ip,
          },
          'medium'
        );

        return reply.code(200).send({
          success: true,
          message: '文件删除成功',
        });
      } catch (error) {
        logger.error('Delete file error:', error);
        return reply.code(500).send({
          success: false,
          message: '删除文件失败',
          error: 'DELETE_FILE_ERROR',
        });
      }
    },
  });

  // 获取上传配置
  fastify.get('/config', {
    handler: async (request: FastifyRequest, reply: FastifyReply) => {
      return reply.code(200).send({
        success: true,
        data: {
          maxFileSize: MAX_FILE_SIZE,
          allowedExtensions: ALLOWED_EXTENSIONS,
          allowedMimeTypes: ALLOWED_MIME_TYPES,
        },
      });
    },
  });
}

/**
 * 确保上传目录存在
 */
async function ensureUploadDir() {
  try {
    await access(UPLOAD_DIR);
  } catch {
    await mkdir(UPLOAD_DIR, { recursive: true });
    logger.info(`Created upload directory: ${UPLOAD_DIR}`);
  }
}
