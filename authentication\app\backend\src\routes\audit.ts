/**
 * 审计日志路由
 */

import { FastifyInstance, FastifyRequest, FastifyReply } from 'fastify';
import { prisma } from '@/config/database';
import { logger } from '@/utils/logger';
import { requireAdmin } from '@/middleware/auth';
import { adminActionRateLimit } from '@/middleware/rateLimit';
import { AuditLogQuerySchema, ExportSchema } from '@/schemas/auth';
import { AuditLogQuery, ExportQuery } from '@/types';

export async function auditRoutes(fastify: FastifyInstance) {
  // 获取审计日志列表
  fastify.get<{ Querystring: AuditLogQuery }>('/', {
    schema: AuditLogQuerySchema,
    preHandler: [fastify.authenticate, requireAdmin()],
    handler: async (request: FastifyRequest<{ Querystring: AuditLogQuery }>, reply: FastifyReply) => {
      try {
        const {
          page = 1,
          limit = 50,
          action,
          userId,
          adminId,
          startDate,
          endDate,
          ipAddress,
        } = request.query;

        const skip = (page - 1) * limit;
        const where: any = {};

        // 构建查询条件
        if (action) {
          where.action = { contains: action };
        }

        if (userId) {
          where.userId = userId;
        }

        if (adminId) {
          where.adminId = adminId;
        }

        if (startDate || endDate) {
          where.createdAt = {};
          if (startDate) {
            where.createdAt.gte = new Date(startDate);
          }
          if (endDate) {
            where.createdAt.lte = new Date(endDate);
          }
        }

        if (ipAddress) {
          where.ipAddress = { contains: ipAddress };
        }

        // 获取审计日志列表和总数
        const [auditLogs, total] = await Promise.all([
          prisma.auditLog.findMany({
            where,
            skip,
            take: limit,
            orderBy: { createdAt: 'desc' },
            include: {
              admin: {
                select: {
                  username: true,
                  email: true,
                },
              },
            },
          }),
          prisma.auditLog.count({ where }),
        ]);

        return reply.code(200).send({
          success: true,
          data: {
            auditLogs,
            pagination: {
              page,
              limit,
              total,
              pages: Math.ceil(total / limit),
            },
          },
        });
      } catch (error) {
        logger.error('Get audit logs error:', error);
        return reply.code(500).send({
          success: false,
          message: '获取审计日志失败',
          error: 'INTERNAL_ERROR',
        });
      }
    },
  });

  // 获取单个审计日志详情
  fastify.get<{ Params: { id: string } }>('/:id', {
    preHandler: [fastify.authenticate, requireAdmin()],
    handler: async (request: FastifyRequest<{ Params: { id: string } }>, reply: FastifyReply) => {
      try {
        const { id } = request.params;

        const auditLog = await prisma.auditLog.findUnique({
          where: { id },
          include: {
            admin: {
              select: {
                id: true,
                username: true,
                email: true,
                role: true,
              },
            },
          },
        });

        if (!auditLog) {
          return reply.code(404).send({
            success: false,
            message: '审计日志不存在',
            error: 'AUDIT_LOG_NOT_FOUND',
          });
        }

        return reply.code(200).send({
          success: true,
          data: auditLog,
        });
      } catch (error) {
        logger.error('Get audit log error:', error);
        return reply.code(500).send({
          success: false,
          message: '获取审计日志详情失败',
          error: 'INTERNAL_ERROR',
        });
      }
    },
  });

  // 获取审计日志统计
  fastify.get('/statistics', {
    preHandler: [fastify.authenticate, requireAdmin()],
    handler: async (request: FastifyRequest, reply: FastifyReply) => {
      try {
        const now = new Date();
        const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
        const thisWeek = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000);
        const thisMonth = new Date(now.getFullYear(), now.getMonth(), 1);

        // 获取统计数据
        const [
          totalLogs,
          todayLogs,
          weekLogs,
          monthLogs,
          actionStats,
          adminStats,
          recentActions,
        ] = await Promise.all([
          // 总日志数
          prisma.auditLog.count(),
          
          // 今日日志数
          prisma.auditLog.count({
            where: {
              createdAt: { gte: today },
            },
          }),
          
          // 本周日志数
          prisma.auditLog.count({
            where: {
              createdAt: { gte: thisWeek },
            },
          }),
          
          // 本月日志数
          prisma.auditLog.count({
            where: {
              createdAt: { gte: thisMonth },
            },
          }),
          
          // 操作类型统计
          prisma.auditLog.groupBy({
            by: ['action'],
            _count: {
              action: true,
            },
            orderBy: {
              _count: {
                action: 'desc',
              },
            },
            take: 10,
          }),
          
          // 管理员操作统计
          prisma.auditLog.groupBy({
            by: ['adminId'],
            _count: {
              adminId: true,
            },
            where: {
              adminId: { not: null },
              createdAt: { gte: thisMonth },
            },
            orderBy: {
              _count: {
                adminId: 'desc',
              },
            },
            take: 10,
          }),
          
          // 最近操作
          prisma.auditLog.findMany({
            take: 20,
            orderBy: { createdAt: 'desc' },
            select: {
              id: true,
              action: true,
              createdAt: true,
              ipAddress: true,
              admin: {
                select: {
                  username: true,
                  email: true,
                },
              },
            },
          }),
        ]);

        // 获取管理员信息
        const adminIds = adminStats.map(stat => stat.adminId).filter(Boolean);
        const admins = await prisma.admin.findMany({
          where: {
            id: { in: adminIds as string[] },
          },
          select: {
            id: true,
            username: true,
            email: true,
          },
        });

        const adminStatsWithNames = adminStats.map(stat => ({
          ...stat,
          admin: admins.find(admin => admin.id === stat.adminId),
        }));

        return reply.code(200).send({
          success: true,
          data: {
            summary: {
              total: totalLogs,
              today: todayLogs,
              week: weekLogs,
              month: monthLogs,
            },
            actionStats: actionStats.map(stat => ({
              action: stat.action,
              count: stat._count.action,
            })),
            adminStats: adminStatsWithNames.map(stat => ({
              adminId: stat.adminId,
              admin: stat.admin,
              count: stat._count.adminId,
            })),
            recentActions,
          },
        });
      } catch (error) {
        logger.error('Get audit statistics error:', error);
        return reply.code(500).send({
          success: false,
          message: '获取审计统计失败',
          error: 'INTERNAL_ERROR',
        });
      }
    },
  });

  // 导出审计日志
  fastify.get<{ Querystring: ExportQuery }>('/export', {
    schema: ExportSchema,
    preHandler: [fastify.authenticate, requireAdmin('admin')],
    handler: async (request: FastifyRequest<{ Querystring: ExportQuery }>, reply: FastifyReply) => {
      try {
        const {
          format = 'csv',
          startDate,
          endDate,
        } = request.query;

        const where: any = {};

        if (startDate || endDate) {
          where.createdAt = {};
          if (startDate) {
            where.createdAt.gte = new Date(startDate);
          }
          if (endDate) {
            where.createdAt.lte = new Date(endDate);
          }
        }

        const auditLogs = await prisma.auditLog.findMany({
          where,
          orderBy: { createdAt: 'desc' },
          include: {
            admin: {
              select: {
                username: true,
                email: true,
              },
            },
          },
        });

        const admin = request.user as any;

        // 记录导出操作
        await prisma.auditLog.create({
          data: {
            action: 'audit_logs_exported',
            details: {
              format,
              count: auditLogs.length,
              startDate,
              endDate,
            },
            adminId: admin.adminId,
            ipAddress: request.ip,
            userAgent: request.headers['user-agent'] || '',
          },
        });

        if (format === 'json') {
          reply.header('Content-Type', 'application/json');
          reply.header('Content-Disposition', `attachment; filename="audit-logs-${new Date().toISOString().split('T')[0]}.json"`);
          
          return reply.code(200).send({
            exportTime: new Date().toISOString(),
            exportedBy: admin.adminId,
            count: auditLogs.length,
            data: auditLogs,
          });
        } else {
          // CSV格式
          const csvHeaders = [
            'ID',
            'Action',
            'User QQ',
            'User Name',
            'Admin Username',
            'Admin Name',
            'IP Address',
            'User Agent',
            'Details',
            'Created At',
          ];

          const csvRows = auditLogs.map(log => [
            log.id,
            log.action,
            log.user?.qq || '',
            '', // user realName not available
            log.admin?.username || '',
            log.admin?.email || '',
            log.ipAddress || '',
            log.userAgent || '',
            JSON.stringify(log.details),
            log.createdAt.toISOString(),
          ]);

          const csvContent = [
            csvHeaders.join(','),
            ...csvRows.map(row => row.map(field => `"${field}"`).join(',')),
          ].join('\n');

          reply.header('Content-Type', 'text/csv; charset=utf-8');
          reply.header('Content-Disposition', `attachment; filename="audit-logs-${new Date().toISOString().split('T')[0]}.csv"`);
          
          return reply.code(200).send(csvContent);
        }
      } catch (error) {
        logger.error('Export audit logs error:', error);
        return reply.code(500).send({
          success: false,
          message: '导出审计日志失败',
          error: 'INTERNAL_ERROR',
        });
      }
    },
  });

  // 清理旧日志
  fastify.post('/cleanup', {
    preHandler: [fastify.authenticate, requireAdmin('admin')],
    handler: async (request: FastifyRequest, reply: FastifyReply) => {
      try {
        const admin = request.user as any;
        
        // 删除90天前的日志
        const cutoffDate = new Date();
        cutoffDate.setDate(cutoffDate.getDate() - 90);

        const result = await prisma.auditLog.deleteMany({
          where: {
            createdAt: {
              lt: cutoffDate,
            },
          },
        });

        // 记录清理操作
        await prisma.auditLog.create({
          data: {
            action: 'audit_logs_cleaned',
            details: {
              deletedCount: result.count,
              cutoffDate: cutoffDate.toISOString(),
            },
            adminId: admin.adminId,
            ipAddress: request.ip,
            userAgent: request.headers['user-agent'] || '',
          },
        });

        return reply.code(200).send({
          success: true,
          message: '审计日志清理完成',
          data: {
            deletedCount: result.count,
            cutoffDate,
          },
        });
      } catch (error) {
        logger.error('Cleanup audit logs error:', error);
        return reply.code(500).send({
          success: false,
          message: '清理审计日志失败',
          error: 'INTERNAL_ERROR',
        });
      }
    },
  });
}
