import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { request } from '@/utils/request'
import { getToken, setToken, removeToken, getUserInfo, setUserInfo, getPermissions, setPermissions } from '@/utils/auth'

export const useAuthStore = defineStore('auth', () => {
  // 状态
  const token = ref(getToken())
  const user = ref(getUserInfo())
  const permissions = ref(getPermissions())
  const isLoading = ref(false)

  // 初始化时的调试信息
  console.log('Auth store 初始化:')
  console.log('- Token:', token.value?.substring(0, 20) + '...' || 'null')
  console.log('- User:', user.value)
  console.log('- Permissions:', permissions.value)

  // 计算属性
  const isAuthenticated = computed(() => !!token.value && !!user.value)
  const isAdmin = computed(() => !!user.value && (user.value.role === 'admin' || user.value.role === 'auditor'))

  // 登录
  const login = async (credentials) => {
    try {
      isLoading.value = true
      const response = await request.post('/api/admin/login', credentials)

      if (response.success) {
        token.value = response.data.token
        // 管理员登录时，用户信息可能在admin字段或user字段中
        user.value = response.data.admin || response.data.user || {
          id: response.data.admin_id || 'unknown',
          username: response.data.username || 'unknown',
          role: response.data.role || 'admin'
        }
        permissions.value = response.data.permissions || []

        setToken(response.data.token)
        setUserInfo(user.value)
        setPermissions(permissions.value)

        // 调试信息
        console.log('登录响应数据:', response.data)
        console.log('设置的用户信息:', user.value)
        console.log('保存的token:', response.data.token.substring(0, 20) + '...')
        console.log('localStorage中的token:', getToken()?.substring(0, 20) + '...')

        // 不在这里显示成功消息，让调用方处理
        return { success: true }
      } else {
        throw new Error(response.message || '登录失败')
      }
    } catch (error) {
      console.error('登录错误:', error, '，loginapi返回' + JSON.stringify(error.response?.data))

      // 优先使用后端返回的错误信息
      let errorMessage = '登录失败'
      if (error.response?.data?.message) {
        errorMessage = error.response.data.message
      } else if (error.message) {
        errorMessage = error.message
      }

      return { success: false, error: errorMessage }
    } finally {
      isLoading.value = false
    }
  }

  // 登出
  const logout = async () => {
    try {
      await request.post('/api/admin/logout')
    } catch (error) {
      console.error('登出错误:', error)
    } finally {
      token.value = null
      user.value = null
      permissions.value = []
      removeToken()
    }
  }

  // 检查认证状态
  const checkAuth = async () => {
    const savedToken = getToken()
    if (!savedToken) {
      console.log('没有保存的token')
      return false
    }

    // 如果已经有用户信息且token匹配，直接返回true
    if (token.value === savedToken && user.value) {
      console.log('使用缓存的认证状态')
      return true
    }

    try {
      console.log('验证token有效性')
      const response = await request.get('/api/admin/profile')
      if (response.success) {
        token.value = savedToken
        // 处理管理员profile数据结构
        user.value = response.data.admin || response.data.user || response.data
        permissions.value = response.data.permissions || []

        console.log('认证检查成功，用户信息:', user.value)
        return true
      } else {
        console.log('Token验证失败:', response)
        // Token无效，清除状态
        token.value = null
        user.value = null
        permissions.value = []
        removeToken()
        return false
      }
    } catch (error) {
      console.error('认证检查失败:', error)
      // 如果是网络错误，不清除token，允许离线使用
      if (error.code === 'NETWORK_ERROR' && savedToken && user.value) {
        console.log('网络错误，使用缓存状态')
        return true
      }

      // 其他错误，清除状态
      token.value = null
      user.value = null
      permissions.value = []
      removeToken()
      return false
    }
  }

  // 刷新token
  const refreshToken = async () => {
    try {
      const response = await request.post('/api/admin/refresh')
      if (response.success) {
        token.value = response.data.token
        setToken(response.data.token)
        return true
      }
      return false
    } catch (error) {
      console.error('刷新token失败:', error)
      return false
    }
  }

  // 更新用户信息
  const updateUser = (userData) => {
    user.value = { ...user.value, ...userData }
  }

  // 检查权限
  const hasPermission = (permission) => {
    if (!user.value) return false

    // 管理员拥有所有权限
    if (user.value.role === 'admin') return true

    // 检查具体权限
    if (permissions.value && Array.isArray(permissions.value)) {
      return permissions.value.includes(permission)
    }

    // 基于角色的简单权限检查
    const rolePermissions = {
      'admin': ['user.view', 'pending.view', 'config.edit', 'logs.view', 'reports.view'],
      'auditor': ['user.view', 'pending.view', 'logs.view', 'reports.view'],
      'staff': ['user.view', 'pending.view']
    }

    const userRole = user.value.role || 'staff'
    return rolePermissions[userRole]?.includes(permission) || false
  }

  // 记录页面访问
  const recordPageVisit = (path) => {
    // 简单的页面访问记录，可以扩展为发送到后端
    console.log('页面访问:', path)
  }

  // 重置状态
  const reset = () => {
    token.value = null
    user.value = null
    permissions.value = []
    isLoading.value = false
  }

  return {
    // 状态
    token,
    user,
    permissions,
    isLoading,

    // 计算属性
    isAuthenticated,
    isAdmin,

    // 方法
    login,
    logout,
    checkAuth,
    refreshToken,
    updateUser,
    hasPermission,
    recordPageVisit,
    reset
  }
})
